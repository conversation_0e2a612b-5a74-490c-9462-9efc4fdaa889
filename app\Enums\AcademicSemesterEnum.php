<?php

namespace App\Enums;

enum AcademicSemesterEnum: string
{
    case ODD = 'odd';
    case EVEN = 'even';

    public static function options(): array
    {
        return [
            self::ODD->value => 'Ganjil',
            self::EVEN->value => 'Genap',
        ];
    }

    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }

    public function toString(): string
    {
        return $this->value;
    }

    public function getFormattedLabel(): string
    {
        return ucfirst(self::getLabel($this));
    }
}
