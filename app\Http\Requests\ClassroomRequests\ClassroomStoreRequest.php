<?php

namespace App\Http\Requests\ClassroomRequests;

use App\Enums\ClassroomLevelEnum;
use App\Enums\StatusEnum;
use Illuminate\Foundation\Http\FormRequest;

class ClassroomStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:100'],
            'level' => ['required', 'string', 'in:'.implode(',', ClassroomLevelEnum::values())],
            'capacity' => ['required', 'integer', 'min:1', 'max:100'],
            'program_id' => ['required', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['required', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'description' => ['nullable', 'string', 'max:500'],
            'status' => ['required', 'string', 'in:'.implode(',', StatusEnum::values())],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama kelas wajib diisi',
            'name.max' => 'Nama kelas maksimal 100 karakter',
            'level.required' => 'Level kelas wajib dipilih',
            'level.in' => 'Level kelas tidak valid',
            'capacity.required' => 'Kapasitas kelas wajib diisi',
            'capacity.integer' => 'Kapasitas kelas harus berupa angka',
            'capacity.min' => 'Kapasitas kelas minimal 1 siswa',
            'capacity.max' => 'Kapasitas kelas maksimal 100 siswa',
            'program_id.required' => 'Program wajib dipilih',
            'program_id.exists' => 'Program tidak ditemukan',
            'shift_id.exists' => 'Shift tidak ditemukan',
            'teacher_id.required' => 'Guru wajib dipilih',
            'teacher_id.exists' => 'Guru tidak ditemukan',
            'academic_year_id.required' => 'Tahun akademik wajib dipilih',
            'academic_year_id.exists' => 'Tahun akademik tidak ditemukan',
            'status.required' => 'Status wajib dipilih',
            'status.in' => 'Status tidak valid',
        ];
    }
}
