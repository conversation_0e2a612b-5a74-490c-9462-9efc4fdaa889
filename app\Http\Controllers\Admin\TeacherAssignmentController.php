<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\TeacherAssignmentException;
use App\Http\Controllers\Controller;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentDeleteRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentFilterRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentStoreRequest;
use App\Http\Requests\TeacherAssignmentRequests\TeacherAssignmentUpdateRequest;
use App\Http\Resources\TeacherAssignmentResource;
use App\Models\TeacherAssignment;
use App\Services\AcademicYearService;
use App\Services\ClassroomService;
use App\Services\SubjectService;
use App\Services\TeacherAssignmentService;
use App\Services\TeacherService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

class TeacherAssignmentController extends Controller
{
    /**
     * Teacher assignment service instance
     */
    protected TeacherAssignmentService $teacherAssignmentService;

    /**
     * Teacher service instance
     */
    protected TeacherService $teacherService;

    /**
     * Subject service instance
     */
    protected SubjectService $subjectService;

    /**
     * Classroom service instance
     */
    protected ClassroomService $classroomService;

    /**
     * Academic year service instance
     */
    protected AcademicYearService $academicYearService;

    /**
     * TeacherAssignmentController constructor
     */
    public function __construct(
        TeacherAssignmentService $teacherAssignmentService,
        TeacherService $teacherService,
        SubjectService $subjectService,
        ClassroomService $classroomService,
        AcademicYearService $academicYearService
    ) {
        $this->teacherAssignmentService = $teacherAssignmentService;
        $this->teacherService = $teacherService;
        $this->subjectService = $subjectService;
        $this->classroomService = $classroomService;
        $this->academicYearService = $academicYearService;
    }

    /**
     * Display a listing of teacher assignments
     */
    public function index(TeacherAssignmentFilterRequest $request): View|JsonResponse|RedirectResponse|StreamedResponse
    {
        // Handle export requests
        if ($request->has('export') || $request->has('export_selected')) {
            return $this->handleExport($request);
        }

        $teacherAssignments = $this->teacherAssignmentService->getAllTeacherAssignments($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($teacherAssignments);
        }

        return view('admin.pages.teacher-assignment.index', [
            'teachers' => $this->teacherService->getAllActiveTeachers(),
            'subjects' => $this->subjectService->getAllSubjects(),
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
        ]);
    }

    /**
     * Format response for DataTables
     */
    private function datatableResponse($data): JsonResponse
    {
        try {
            $dataTable = datatables()
                ->of($data)
                ->addIndexColumn()
                ->editColumn('teacher.user.name', function ($row) {
                    return $row->teacher?->user?->name ?? '<span class="text-muted">-</span>';
                })
                ->editColumn('subject.name', function ($row) {
                    return $row->subject?->name ?? '<span class="text-muted">Tidak ada mata pelajaran</span>';
                })
                ->editColumn('classroom.name', function ($row) {
                    return $row->classroom?->name ?? '<span class="text-muted">-</span>';
                })
                ->editColumn('academicYear.name', function ($row) {
                    return $row->academicYear?->name ?? '<span class="text-muted">-</span>';
                })
                ->editColumn('is_homeroom_teacher', function ($row) {
                    if ($row->is_homeroom_teacher) {
                        return '<span class="badge bg-success-subtle text-success">
                                    <i class="ri-user-star-line align-bottom"></i> Wali Kelas
                                </span>';
                    }
                    return '<span class="badge bg-info-subtle text-info">
                                <i class="ri-book-open-line align-bottom"></i> Guru Mapel
                            </span>';
                })
                ->addColumn('info', function ($row) {
                    $info = '';
                    if ($row->teacher?->user) {
                        $info .= '<small class="text-muted d-block">Email: ' . ($row->teacher->user->email ?? '-') . '</small>';
                    }
                    if ($row->subject) {
                        $info .= '<small class="text-muted d-block">Program: ' . ($row->subject->program?->name ?? '-') . '</small>';
                    }
                    return $info;
                })
                ->addColumn('action', function ($row) {
                    return view('admin.pages.teacher-assignment.action', ['item' => $row])->render();
                })
                ->rawColumns(['teacher.user.name', 'subject.name', 'classroom.name', 'academicYear.name', 'is_homeroom_teacher', 'info', 'action']);

            // Add summary statistics to the response
            $response = $dataTable->make(true);
            $responseData = $response->getData(true);

            // Calculate summary statistics
            $summary = $this->calculateSummaryStats($data);
            $responseData['summary'] = $summary;

            return response()->json($responseData);
        } catch (\Exception $e) {
            \Log::error('Error in teacher assignments datatable: ' . $e->getMessage());
            return response()->json([
                'error' => 'Terjadi kesalahan saat memuat data penugasan guru',
                'draw' => request()->get('draw', 1),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ], 500);
        }
    }

    /**
     * Calculate summary statistics
     */
    private function calculateSummaryStats($data): array
    {
        $total = $data->count();
        $homeroom = $data->where('is_homeroom_teacher', true)->count();
        $teachers = $data->pluck('teacher_id')->unique()->count();
        $subjects = $data->pluck('subject_id')->whereNotNull()->unique()->count();

        return [
            'total' => $total,
            'homeroom' => $homeroom,
            'teachers' => $teachers,
            'subjects' => $subjects
        ];
    }

    /**
     * Handle export requests
     */
    private function handleExport($request)
    {
        try {
            if ($request->has('export_selected')) {
                return $this->exportSelected($request);
            }

            $filters = $request->validated();
            $teacherAssignments = $this->teacherAssignmentService->getAllTeacherAssignments($filters);

            return $this->exportToExcel($teacherAssignments, 'teacher-assignments-' . date('Y-m-d'));
        } catch (\Exception $e) {
            \Log::error('Export error: ' . $e->getMessage());
            return back()->with('error', 'Terjadi kesalahan saat mengekspor data');
        }
    }

    /**
     * Export selected items
     */
    private function exportSelected($request)
    {
        $selectedIds = $request->input('selected_ids', []);

        if (empty($selectedIds)) {
            return back()->with('error', 'Tidak ada data yang dipilih untuk diekspor');
        }

        $teacherAssignments = collect();
        foreach ($selectedIds as $id) {
            try {
                $assignment = $this->teacherAssignmentService->getTeacherAssignmentById($id);
                $teacherAssignments->push($assignment);
            } catch (\Exception $e) {
                // Skip invalid IDs
                continue;
            }
        }

        return $this->exportToExcel($teacherAssignments, 'selected-teacher-assignments-' . date('Y-m-d'));
    }

    /**
     * Export data to Excel
     */
    private function exportToExcel($data, $filename)
    {
        $exportData = [];
        $exportData[] = ['No', 'Nama Guru', 'Email Guru', 'Mata Pelajaran', 'Kelas', 'Tahun Akademik', 'Status', 'Program'];

        foreach ($data as $index => $assignment) {
            $exportData[] = [
                $index + 1,
                $assignment->teacher?->user?->name ?? '-',
                $assignment->teacher?->user?->email ?? '-',
                $assignment->subject?->name ?? 'Tidak ada mata pelajaran',
                $assignment->classroom?->name ?? '-',
                $assignment->academicYear?->name ?? '-',
                $assignment->is_homeroom_teacher ? 'Wali Kelas' : 'Guru Mapel',
                $assignment->subject?->program?->name ?? '-'
            ];
        }

        // Create a simple CSV export (you can enhance this with a proper Excel library)
        $filename = $filename . '.csv';

        $response = response()->streamDownload(function () use ($exportData) {
            $file = fopen('php://output', 'w');
            foreach ($exportData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);

        return $response;
    }

    /**
     * Show the form for creating a new teacher assignment
     */
    public function create(): View
    {
        return view('admin.pages.teacher-assignment.create', [
            'teachers' => $this->teacherService->getAllActiveTeachers(),
            'subjects' => $this->subjectService->getAllSubjects(),
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
        ]);
    }

    /**
     * Store a newly created teacher assignment
     */
    public function store(TeacherAssignmentStoreRequest $request): JsonResponse
    {
        try {
            $teacherAssignment = $this->teacherAssignmentService->createTeacherAssignment($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dibuat',
                'data' => new TeacherAssignmentResource($teacherAssignment),
            ]);
        } catch (TeacherAssignmentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            \Log::error('Error creating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat penugasan guru',
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified teacher assignment
     */
    public function edit(TeacherAssignment $teacherAssignment): View
    {
        // Eager load relationships to avoid N+1 issues
        $teacherAssignment->load(['teacher.user', 'subject', 'classroom', 'academicYear']);

        return view('admin.pages.teacher-assignment.edit', [
            'teacherAssignment' => $teacherAssignment,
            'teachers' => $this->teacherService->getAllActiveTeachers(),
            'subjects' => $this->subjectService->getAllSubjects(),
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
        ]);
    }

    /**
     * Update the specified teacher assignment
     */
    public function update(TeacherAssignmentUpdateRequest $request, TeacherAssignment $teacherAssignment): JsonResponse
    {
        try {
            $result = $this->teacherAssignmentService->updateTeacherAssignment($teacherAssignment->id, $request->validated());

            if (!$result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tidak ada perubahan data',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil diperbarui',
                'data' => new TeacherAssignmentResource($teacherAssignment->fresh()),
            ]);
        } catch (TeacherAssignmentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            \Log::error('Error updating teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui penugasan guru',
            ], 500);
        }
    }

    /**
     * Remove the specified teacher assignment
     */
    public function destroy(TeacherAssignmentDeleteRequest $request, TeacherAssignment $teacherAssignment): JsonResponse
    {
        try {
            $this->teacherAssignmentService->deleteTeacherAssignment($teacherAssignment->id);

            return response()->json([
                'success' => true,
                'message' => 'Penugasan guru berhasil dihapus',
            ]);
        } catch (TeacherAssignmentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            \Log::error('Error deleting teacher assignment: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus penugasan guru',
            ], 500);
        }
    }
}


