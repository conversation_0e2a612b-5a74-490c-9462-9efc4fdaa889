<?php

namespace App\Http\Requests\TeacherAssignmentRequests;

use Illuminate\Foundation\Http\FormRequest;

class TeacherAssignmentFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'teacher_id' => 'nullable|integer|exists:teachers,id',
            'subject_id' => 'nullable|integer|exists:subjects,id',
            'classroom_id' => 'nullable|integer|exists:classrooms,id',
            'academic_year_id' => 'nullable|integer|exists:academic_years,id',
            'is_homeroom_teacher' => 'nullable|boolean',
            'search' => 'nullable|string|max:255',
        ];
    }
}
