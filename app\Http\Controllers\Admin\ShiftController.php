<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ShiftRequests\ShiftFilterRequest;
use App\Http\Requests\ShiftRequests\ShiftStoreRequest;
use App\Http\Requests\ShiftRequests\ShiftUpdateRequest;
use App\Services\ClassroomService;
use App\Services\LessonHourService;
use App\Services\ShiftService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class ShiftController extends Controller
{
    /**
     * The shift service instance.
     */
    protected ShiftService $shiftService;

    protected ClassroomService $classroomService;

    protected LessonHourService $lessonHourService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        ShiftService $shiftService,
        ClassroomService $classroomService,
        LessonHourService $lessonHourService
    ) {
        $this->shiftService = $shiftService;
        $this->classroomService = $classroomService;
        $this->lessonHourService = $lessonHourService;
    }

    /**
     * Display a listing of shifts
     *
     * @return View|JsonResponse
     */
    public function index(ShiftFilterRequest $request)
    {
        $shifts = $this->shiftService->getAllShifts($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($shifts);
        }

        return view('admin.pages.shift.index', [
            'shifts' => $shifts,
        ]);
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        $result = [];
        $counter = 0;

        foreach ($data as $item) {
            $counter++;
            $result[] = [
                'DT_RowIndex' => $counter,
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description,
                'status' => $item->status,
                'action' => view('admin.pages.shift.action', compact('item'))->render(),
            ];
        }

        return response()->json([
            'draw' => request()->input('draw'),
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $result,
        ]);
    }

    /**
     * Show the form for creating a new shift
     */
    public function create(): View
    {
        return view('admin.pages.shift.create');
    }

    /**
     * Store a newly created shift
     */
    public function store(ShiftStoreRequest $request): JsonResponse
    {
        try {
            $shift = $this->shiftService->createShift($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Shift berhasil dibuat',
                'data' => $shift,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Show the form for editing the specified shift
     */
    public function edit(int $id): View
    {
        $shift = $this->shiftService->getShiftById($id);

        return view('admin.pages.shift.edit', [
            'shift' => $shift,
        ]);
    }

    /**
     * Update the specified shift
     */
    public function update(ShiftUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $result = $this->shiftService->updateShift($id, $request->validated());

            if (! $result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tidak ada perubahan data',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Shift berhasil diperbarui',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Display the specified shift
     */
    public function show(int $id): View
    {
        $shift = $this->shiftService->getShiftById($id);

        // Load classrooms with this shift
        $classrooms = $this->classroomService->getClassroomsByShift($id);

        // Load lesson hours with this shift
        $lessonHours = $this->lessonHourService->getLessonHoursByShift($id);

        // Count classrooms for this shift
        $shift->classrooms_count = $classrooms->count();

        return view('admin.pages.shift.show', [
            'shift' => $shift,
            'classrooms' => $classrooms,
            'lessonHours' => $lessonHours,
        ]);
    }

    /**
     * Remove the specified shift
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->shiftService->deleteShift($id);

            return response()->json([
                'success' => true,
                'message' => 'Shift berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}
