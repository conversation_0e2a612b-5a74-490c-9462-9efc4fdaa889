<?php

namespace App\Models;

use App\Enums\ClassroomLevelEnum;
use App\Enums\StatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Classroom extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'level',
        'capacity',
        'program_id',
        'shift_id',
        'teacher_id',
        'academic_year_id',
        'description',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'level' => ClassroomLevelEnum::class,
        'status' => StatusEnum::class,
    ];

    /**
     * Get the program that owns the classroom.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the shift that owns the classroom.
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the teacher that owns the classroom.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the academic year that owns the classroom.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the lesson hours for the classroom.
     */
    public function lessonHours(): HasMany
    {
        return $this->hasMany(LessonHour::class);
    }

    /**
     * Get the students for the classroom.
     */
    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'classroom_students')
            ->withPivot('academic_year_id')
            ->using(ClassroomStudent::class)
            ->withTimestamps();
    }

    /**
     * Get the current students for the classroom.
     */
    public function currentStudents()
    {
        return $this->students()
            ->wherePivot('academic_year_id', $this->academic_year_id);
    }

    /**
     * Get the attendance records for the classroom.
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the available capacity of the classroom.
     */
    public function getAvailableCapacityAttribute(): int
    {
        $enrolledCount = $this->currentStudents()->count();

        return max(0, $this->capacity - $enrolledCount);
    }

    /**
     * Scope a query to only include active classrooms.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Check if the classroom is at capacity.
     */
    public function isAtCapacity(): bool
    {
        return $this->available_capacity <= 0;
    }

    /**
     * Get formatted level name.
     */
    public function getLevelNameAttribute(): string
    {
        return ClassroomLevelEnum::getLabel($this->level);
    }

    /**
     * Scope a query to only include classrooms with active academic year.
     */
    public function scopeWithActiveAcademicYear($query)
    {
        return $query->whereHas('academicYear', function ($query) {
            $query->where('status', 'active');
        });
    }

    /**
     * Get the teacher assignments for the classroom.
     */
    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}
