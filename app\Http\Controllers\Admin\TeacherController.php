<?php

namespace App\Http\Controllers\Admin;

use Throwable;
use App\Enums\RoleEnum;
use App\Models\Teacher;
use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\TeacherRequests\TeacherStoreRequest;
use App\Http\Requests\TeacherRequests\TeacherFilterRequest;
use App\Http\Requests\TeacherRequests\TeacherUpdateRequest;

class TeacherController extends Controller
{
    /**
     * Display a listing of teachers
     */
    public function index(TeacherFilterRequest $request): View|JsonResponse
    {
        $query = Teacher::with(['user.roles:id,name'])
            ->select([
                'teachers.id',
                'teachers.user_id',
                'teachers.gender',
                'teachers.birth_place',
                'teachers.birth_date',
                'teachers.created_at'
            ])
            ->join('users', 'teachers.user_id', '=', 'users.id')
            ->whereHas('user.roles', function ($q) {
                $q->whereIn('name', RoleEnum::teacher());
            });

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatTeachersForDatatable($query);
        }

        return view('admin.pages.teacher.index', [
            'statuses' => UserStatus::dropdown(),
            'roles' => RoleEnum::teacherOptions(),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Apply filters to the query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by gender
        if (!empty($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        // Filter by status
        if (!empty($filters['status'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('status', $filters['status']);
            });
        }

        // Filter by role
        if (!empty($filters['role'])) {
            $query->whereHas('user.roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        // Search filter
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->whereHas('user', function ($q) use ($searchTerm) {
                $q->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', $searchTerm)
                        ->orWhere('email', 'like', $searchTerm)
                        ->orWhere('phone_number', 'like', $searchTerm);
                });
            });
        }

        // Default sorting
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables
     */
    protected function formatTeachersForDatatable($query): JsonResponse|string
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
            ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
            ->editColumn('user.role', fn($row) => $row->user->roleLabel() ?? '-')
            ->editColumn('gender', function ($row) {
                return $row->gender?->label() ?? '-';
            })
            ->editColumn('birth_date', fn($row) => $row->birth_date?->format('d/m/Y') ?? '-')
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.teacher._action', [
                    'id' => $row->id,
                    'edit' => route('admin.teachers.edit', $row->id),
                    'destroy' => route('admin.teachers.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new teacher
     */
    public function create(): View
    {
        return view('admin.pages.teacher.create', [
            'roles' => RoleEnum::teacherOptions(),
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Store a newly created teacher
     */
    public function store(TeacherStoreRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            $user = User::create([
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'] ?? null,
                'status' => $validated['status'],
                'password' => bcrypt($validated['password']),
            ]);

            $user->assignRole($validated['role']);

            $teacher = Teacher::create([
                'user_id' => $user->id,
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dibuat.',
                'data' => $teacher->load('user'),
            ], Response::HTTP_CREATED);

        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat data guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified teacher
     */
    public function edit(int $id): View
    {
        $teacher = Teacher::with(['user.roles:id,name'])->findOrFail($id);

        return view('admin.pages.teacher.edit', [
            'teacher' => $teacher,
            'roles' => RoleEnum::teacherOptions(),
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'currentRole' => $teacher->user->getRoleNames()->first(),
        ]);
    }

    /**
     * Update the specified teacher
     */
    public function update(TeacherUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $teacher = Teacher::with('user')->findOrFail($id);
            $validated = $request->validated();

            // Update user data
            $userData = [
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'] ?? null,
                'status' => $validated['status'],
            ];

            if (!empty($validated['password'])) {
                $userData['password'] = bcrypt($validated['password']);
            }

            $teacher->user->update($userData);
            $teacher->user->syncRoles($validated['role']);

            // Update teacher data
            $teacher->update([
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil diperbarui.',
            ]);

        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified teacher
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $teacher = Teacher::with('user')->findOrFail($id);

            // Prevent deleting yourself
            if ($teacher->user_id === auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus akun sendiri.',
                ], Response::HTTP_FORBIDDEN);
            }

            // prevent deleting active user
            if ($teacher->user->status === UserStatus::Active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus akun yang masih aktif.',
                ], Response::HTTP_FORBIDDEN);
            }

            // Delete both teacher and user records
            $teacher->user->delete();
            $teacher->delete();

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dihapus.',
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data guru.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
