<?php

namespace Database\Seeders;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Models\Student;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currentYear = now()->year;
        $students = [];

        // Common Indonesian names for more realistic data
        $maleNames = ['<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'];
        $femaleNames = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'];
        $lastNames = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ya'];

        for ($i = 1; $i <= 50; $i++) {
            $isMale = $i % 2 == 0;
            $firstName = $isMale ? $maleNames[array_rand($maleNames)] : $femaleNames[array_rand($femaleNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $fullName = $firstName.' '.$lastName;

            $user = User::create([
                'username' => 'student'.$i,
                'name' => $fullName,
                'email' => 'student'.$i.'@example.com',
                'password' => Hash::make('password'),
                'phone_number' => '0812345678'.$i,
                'status' => UserStatus::Active->value,
                'remember_token' => Str::random(10),
            ]);
            $user->assignRole(RoleEnum::STUDENT->value);

            $students[] = Student::create([
                'user_id' => $user->id,
                'nis' => str_pad($i, 8, '0', STR_PAD_LEFT),
                'nisn' => str_pad($i, 10, '0', STR_PAD_LEFT),
                'birth_place' => ['Jakarta', 'Bandung', 'Surabaya', 'Medan', 'Semarang', 'Yogyakarta', 'Makassar', 'Palembang', 'Denpasar', 'Manado'][array_rand(['Jakarta', 'Bandung', 'Surabaya', 'Medan', 'Semarang', 'Yogyakarta', 'Makassar', 'Palembang', 'Denpasar', 'Manado'])],
                'birth_date' => now()->subYears(15 + ($i % 3))->subMonths(rand(0, 11))->subDays(rand(0, 30)),
                'gender' => $isMale ? 'male' : 'female',
                'religion' => ['islam', 'kristen', 'katolik', 'hindu', 'buddha', 'konghucu'][$i % 6],
                'phone' => $user->phone_number,
                'address' => 'Jalan '.['Merdeka', 'Sudirman', 'Gatot Subroto', 'Thamrin', 'Asia Afrika', 'Diponegoro', 'Pahlawan', 'Ahmad Yani', 'Hayam Wuruk', 'Pemuda'][array_rand(['Merdeka', 'Sudirman', 'Gatot Subroto', 'Thamrin', 'Asia Afrika', 'Diponegoro', 'Pahlawan', 'Ahmad Yani', 'Hayam Wuruk', 'Pemuda'])].' No. '.$i,
                'parent_name' => 'Bapak/Ibu '.$lastName,
                'parent_phone' => '0812345678'.($i + 100),
                'parent_occupation' => ['PNS', 'Wiraswasta', 'Guru', 'Dokter', 'Pengusaha', 'Karyawan Swasta', 'TNI/Polri', 'Petani', 'Nelayan', 'Buruh'][$i % 10],
                'parent_address' => 'Jalan '.['Merdeka', 'Sudirman', 'Gatot Subroto', 'Thamrin', 'Asia Afrika', 'Diponegoro', 'Pahlawan', 'Ahmad Yani', 'Hayam Wuruk', 'Pemuda'][array_rand(['Merdeka', 'Sudirman', 'Gatot Subroto', 'Thamrin', 'Asia Afrika', 'Diponegoro', 'Pahlawan', 'Ahmad Yani', 'Hayam Wuruk', 'Pemuda'])].' No. '.($i + 100),
                'entry_year' => $currentYear - ($i % 3),
                'profile_picture' => null,
            ]);
        }
    }
}
