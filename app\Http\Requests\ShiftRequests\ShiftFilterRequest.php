<?php

namespace App\Http\Requests\ShiftRequests;

use Illuminate\Foundation\Http\FormRequest;

class ShiftFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'status' => 'nullable|in:active,inactive',
            'search' => 'nullable|string',
        ];
    }
}
