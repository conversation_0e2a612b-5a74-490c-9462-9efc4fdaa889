<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SubjectRequests\SubjectFilterRequest;
use App\Http\Requests\SubjectRequests\SubjectStoreRequest;
use App\Http\Requests\SubjectRequests\SubjectUpdateRequest;
use App\Services\ProgramService;
use App\Services\SubjectService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    /**
     * Subject service instance
     */
    protected SubjectService $subjectService;

    /**
     * Program service instance
     */
    protected ProgramService $programService;

    /**
     * SubjectController constructor
     */
    public function __construct(SubjectService $subjectService, ProgramService $programService)
    {
        $this->subjectService = $subjectService;
        $this->programService = $programService;
    }

    /**
     * Display a listing of subjects
     *
     * @return View|JsonResponse
     */
    public function index(SubjectFilterRequest $request)
    {
        $subjects = $this->subjectService->getAllSubjects($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($subjects);
        }

        return view('admin.pages.subject.index', [
            'subjects' => $subjects,
            'programs' => $this->programService->getAllPrograms(),
        ]);
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        $result = [];
        $counter = 0;

        foreach ($data as $item) {
            $counter++;
            $result[] = [
                'DT_RowIndex' => $counter,
                'id' => $item->id,
                'name' => $item->name,
                'program' => $item->program ? $item->program->name : '-',
                'action' => view('admin.pages.subject.action', compact('item'))->render(),
            ];
        }

        return response()->json([
            'draw' => request()->input('draw'),
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $result,
        ]);
    }

    /**
     * Get subjects by program ID for AJAX requests
     */
    public function getData(Request $request): JsonResponse
    {
        $request->validate([
            'program_id' => 'required|exists:programs,id',
        ]);

        $subjects = $this->subjectService->getAllSubjects([
            'program_id' => $request->program_id,
        ]);

        return response()->json([
            'success' => true,
            'data' => $subjects->map(fn ($subject) => [
                'id' => $subject->id,
                'name' => $subject->name,
            ]),
        ]);
    }

    /**
     * Show the form for creating a new subject
     */
    public function create(): View
    {
        return view('admin.pages.subject.create', [
            'programs' => $this->programService->getAllPrograms(),
        ]);
    }

    /**
     * Store a newly created subject
     */
    public function store(SubjectStoreRequest $request): JsonResponse
    {
        try {
            $subject = $this->subjectService->createSubject($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil dibuat',
                'data' => $subject,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Show the form for editing the specified subject
     */
    public function edit(int $id): View
    {
        $subject = $this->subjectService->getSubjectById($id);

        return view('admin.pages.subject.edit', [
            'subject' => $subject,
            'programs' => $this->programService->getAllPrograms(),
        ]);
    }

    /**
     * Update the specified subject
     */
    public function update(SubjectUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $result = $this->subjectService->updateSubject($id, $request->validated());

            if (! $result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tidak ada perubahan data',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil diperbarui',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Remove the specified subject
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->subjectService->deleteSubject($id);

            return response()->json([
                'success' => true,
                'message' => 'Mata pelajaran berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}
