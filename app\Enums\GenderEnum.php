<?php

namespace App\Enums;

enum GenderEnum: string
{
    case MALE = 'male';
    case FEMALE = 'female';

    /**
     * Get all gender options as an array
     */
    public static function options(): array
    {
        return [
            self::MALE->value => 'Laki-laki',
            self::FEMALE->value => 'Perempuan',
        ];
    }

    /**
     * Get the display label for a gender value
     */
    public static function getLabel(?string $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get the display label for the current enum instance
     */
    public function label(): string
    {
        return self::getLabel($this->value);
    }
}
