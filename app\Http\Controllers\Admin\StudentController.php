<?php

namespace App\Http\Controllers\Admin;

use Throwable;
use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exports\StudentExport;
use App\Exports\StudentTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StudentRequests\StudentStoreRequest;
use App\Http\Requests\StudentRequests\StudentUpdateRequest;
use App\Http\Requests\StudentRequests\StudentFilterRequest;
use App\Models\Student;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index(StudentFilterRequest $request): View|JsonResponse
    {
        $query = Student::with(['user.roles:id,name', 'classrooms'])
            ->select([
                'students.id',
                'students.user_id',
                'students.nis',
                'students.nisn',
                'students.gender',
                'students.birth_place',
                'students.birth_date',
                'students.parent_name',
                'students.parent_phone',
                'students.created_at'
            ])
            ->join('users', 'students.user_id', '=', 'users.id')
            ->whereHas('user.roles', function ($q) {
                $q->where('name', RoleEnum::STUDENT->value);
            });

        $query = $this->applyFilters($query, $request->validated());

        if ($request->ajax()) {
            return $this->formatStudentsForDatatable($query);
        }

        return view('admin.pages.student.index', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Apply filters to the query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by gender
        if (!empty($filters['gender'])) {
            $query->where('students.gender', $filters['gender']);
        }

        // Filter by status
        if (!empty($filters['status'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('status', $filters['status']);
            });
        }

        // Filter by classroom
        if (!empty($filters['classroom_id'])) {
            $query->whereHas('classrooms', function ($q) use ($filters) {
                $q->where('classroom_id', $filters['classroom_id']);
            });
        }

        // Filter by NIS
        if (!empty($filters['nis'])) {
            $query->where('students.nis', 'like', '%' . $filters['nis'] . '%');
        }

        // Filter by NISN
        if (!empty($filters['nisn'])) {
            $query->where('students.nisn', 'like', '%' . $filters['nisn'] . '%');
        }

        // Global search
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', '%' . $search . '%')
                        ->orWhere('email', 'like', '%' . $search . '%')
                        ->orWhere('username', 'like', '%' . $search . '%')
                        ->orWhere('phone_number', 'like', '%' . $search . '%');
                })
                    ->orWhere('students.nis', 'like', '%' . $search . '%')
                    ->orWhere('students.nisn', 'like', '%' . $search . '%')
                    ->orWhere('students.parent_name', 'like', '%' . $search . '%');
            });
        }

        // Default sorting
        $query->orderBy('students.created_at', 'desc');

        return $query;
    }

    /**
     * Format response for DataTables.
     */
    protected function formatStudentsForDatatable($query): JsonResponse|string
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
            ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
            ->editColumn('nis', fn($row) => $row->nis ?? '-')
            ->editColumn('nisn', fn($row) => $row->nisn ?? '-')
            ->editColumn('gender', fn($row) => $row->gender?->label() ?? '-')
            ->editColumn('birth_date', fn($row) => $row->birth_date?->format('d/m/Y') ?? '-')
            ->editColumn('classrooms', function ($row) {
                $classrooms = $row->classrooms;
                if ($classrooms->isEmpty()) {
                    return '<span class="badge bg-warning-subtle text-warning">Belum ada kelas</span>';
                }
                return $classrooms->map(fn($c) => '<span class="badge bg-primary-subtle text-primary">' . $c->name . '</span>')->implode(' ');
            })
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.student._action', [
                    'id' => $row->id,
                    'edit' => route('admin.students.edit', $row->id),
                    'destroy' => route('admin.students.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['status', 'action', 'classrooms'])
            ->toJson();
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): View
    {
        return view('admin.pages.student.create', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options()
        ]);
    }

    /**
     * Store a newly created student.
     */
    public function store(StudentStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $user = User::create([
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => bcrypt($validated['password']),
                'phone_number' => $validated['phone_number'] ?? null,
                'status' => $validated['status'],
            ]);

            $user->assignRole(RoleEnum::STUDENT->value);

            $student = Student::create([
                'user_id' => $user->id,
                'nis' => $validated['nis'],
                'nisn' => $validated['nisn'],
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
                'parent_name' => $validated['parent_name'] ?? null,
                'parent_phone' => $validated['parent_phone'] ?? null,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil dibuat.',
                'data' => $student->load('user'),
            ], Response::HTTP_CREATED);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified student.
     */
    public function show(Student $student)
    {
        //
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(Student $student): View
    {
        $student->load('user');

        return view('admin.pages.student.edit', [
            'student' => $student,
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Update the specified student.
     */
    public function update(StudentUpdateRequest $request, Student $student): JsonResponse
    {
        DB::beginTransaction();
        try {
            $student = Student::with('user')->findOrFail($student->id);
            $validated = $request->validated();

            // Update user data
            $userData = [
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'] ?? null,
                'status' => $validated['status'],
            ];

            if (!empty($validated['password'])) {
                $userData['password'] = bcrypt($validated['password']);
            }

            $student->user->update($userData);

            // Update student data
            $studentData = [
                'nis' => $validated['nis'],
                'nisn' => $validated['nisn'],
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
                'parent_name' => $validated['parent_name'] ?? null,
                'parent_phone' => $validated['parent_phone'] ?? null,
            ];

            $student->update($studentData);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil diperbarui.',
                'data' => $student->load('user'),
            ]);

        } catch (Throwable $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified student.
     */
    public function destroy(Student $student): JsonResponse
    {
        try {
            $student = Student::with('user')->findOrFail($student->id);

            // Prevent deleting yourself
            if ($student->user_id === auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus akun sendiri.',
                ], Response::HTTP_FORBIDDEN);
            }

            // Prevent deleting active user
            if ($student->user->status === UserStatus::Active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus akun yang masih aktif.',
                ], Response::HTTP_FORBIDDEN);
            }

            // Delete both student and user records
            $student->user->delete();
            $student->delete();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil dihapus.',
            ]);

        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Import students data from Excel.
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ]);

        try {
            // You may want to implement the import logic here
            // or create a dedicated import class

            //implement import soon
            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil diimpor.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengimpor data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Export students data to Excel.
     */
    public function export(Request $request)
    {
        $filters = $request->all();
        return Excel::download(
            new StudentExport($filters),
            'daftar-siswa-' . date('Y-m-d-His') . '.xlsx'
        );
    }

    /**
     * Download import template.
     */
    public function template()
    {
        return Excel::download(
            new StudentTemplateExport(),
            'template-impor-siswa.xlsx'
        );
    }
}
