<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exports\StudentExport;
use App\Exports\StudentTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StudentRequests\StudentStoreRequest;
use App\Http\Requests\StudentRequests\StudentUpdateRequest;
use App\Models\Student;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = Student::with(['user', 'classrooms']);

        if ($request->ajax()) {
            return $this->formatStudentsForDatatable($query);
        }

        $classrooms = [];

        return view('admin.pages.student.index', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'classrooms' => $classrooms,
        ]);
    }

    /**
     * Format response for DataTables.
     */
    protected function formatStudentsForDatatable($query): JsonResponse
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
            ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
            ->editColumn('gender', fn($row) => $row->gender?->label() ?? '-')
            ->editColumn('birth_date', fn($row) => $row->birth_date?->format('d/m/Y') ?? '-')
            ->editColumn('classrooms', function ($row) {
                $classrooms = $row->classrooms;
                if ($classrooms->isEmpty()) {
                    return '<span class="badge bg-warning-subtle text-warning">Belum ada kelas</span>';
                }
                return $classrooms->map(fn($c) => '<span class="badge bg-primary-subtle text-primary">' . $c->name . '</span>')->implode(' ');
            })
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.components.student-actions', [
                    'id' => $row->id,
                    'editUrl' => route('admin.students.edit', $row->id),
                    'viewUrl' => route('admin.students.show', $row->id),
                    'enrollUrl' => route('admin.students.enroll', $row->id),
                    'deleteUrl' => route('admin.students.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['action', 'classrooms', 'status'])
            ->make(true);
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): View
    {
        return view('admin.pages.student.create', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options()
        ]);
    }

    /**
     * Store a newly created student.
     */
    public function store(StudentStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $user = User::create([
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'status' => $validated['status'],
                'phone_number' => $validated['phone_number'] ?? null,
            ]);

            Student::create([
                'user_id' => $user->id,
                'nis' => $validated['nis'],
                'nisn' => $validated['nisn'],
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'gender' => $validated['gender'],
                'parent_name' => $validated['parent_name'] ?? null,
                'parent_phone' => $validated['parent_phone'] ?? null,
            ]);

            $user->assignRole(RoleEnum::STUDENT->value);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil ditambahkan.',
            ], Response::HTTP_CREATED);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified student.
     */
    public function show(Student $student)
    {
        //
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(Student $student): View
    {
        $student->load('user');

        return view('admin.pages.student.edit', [
            'student' => $student,
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Update the specified student.
     */
    public function update(StudentUpdateRequest $request, Student $student): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validated();

            $student->user->update([
                'username' => $validated['username'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'status' => $validated['status'],
                'phone_number' => $validated['phone_number'] ?? null,
                'password' => $validated['password'] ? Hash::make($validated['password']) : $student->user->password,
            ]);

            $studentData = [
                'nis' => $validated['nis'],
                'nisn' => $validated['nisn'],
                'gender' => $validated['gender'],
                'birth_place' => $validated['birth_place'],
                'birth_date' => $validated['birth_date'],
                'parent_name' => $validated['parent_name'] ?? null,
                'parent_phone' => $validated['parent_phone'] ?? null,
            ];

            $student->update($studentData);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil diperbarui.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified student.
     */
    public function destroy(Student $student): JsonResponse
    {
        DB::beginTransaction();
        try {
            $student->user->delete();
            $student->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil dihapus.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Import students data from Excel.
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ]);

        try {
            // You may want to implement the import logic here
            // or create a dedicated import class

            //implement import soon
            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil diimpor.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengimpor data siswa.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Export students data to Excel.
     */
    public function export(Request $request)
    {
        $filters = $request->all();
        return Excel::download(
            new StudentExport($filters),
            'daftar-siswa-' . date('Y-m-d-His') . '.xlsx'
        );
    }

    /**
     * Download import template.
     */
    public function template()
    {
        return Excel::download(
            new StudentTemplateExport(),
            'template-impor-siswa.xlsx'
        );
    }
}
