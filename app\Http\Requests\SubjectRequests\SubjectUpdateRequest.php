<?php

namespace App\Http\Requests\SubjectRequests;

use Illuminate\Foundation\Http\FormRequest;

class SubjectUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'program_id' => 'required|integer|exists:programs,id',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Nama mata pelajaran wajib diisi',
            'name.max' => 'Nama mata pelajaran maksimal 255 karakter',
            'program_id.required' => 'Program wajib dipilih',
            'program_id.exists' => 'Program yang dipilih tidak valid',
        ];
    }
}
