<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AcademicSemesterEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\AcademicYearRequests\AcademicYearStoreRequest;
use App\Http\Requests\AcademicYearRequests\AcademicYearUpdateRequest;
use App\Models\AcademicYear;
use App\Services\AcademicYearService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AcademicYearController extends Controller
{
    protected $academicYearService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(AcademicYearService $academicYearService)
    {
        $this->academicYearService = $academicYearService;
    }

    /**
     * Display a listing of the academic years.
     *
     * @return View|JsonResponse
     */
    public function index(Request $request)
    {
        $academicYears = $this->academicYearService->getAllAcademicYears($request->all());

        if ($request->ajax()) {
            return $this->datatableResponse($academicYears);
        }

        return view('admin.pages.academic-year.index');
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->addColumn('semester_label', function ($row) {
                return '<span class="badge bg-soft-info text-info">' . $row->semester_name . '</span>';
            })
            ->addColumn('start_date_formatted', function ($row) {
                return $row->formatted_start_date;
            })
            ->addColumn('end_date_formatted', function ($row) {
                return $row->formatted_end_date;
            })
            ->addColumn('status_label', function ($row) {
                $statusClass = match ($row->status?->value) {
                    'planned' => 'bg-soft-warning text-warning',
                    'active' => 'bg-soft-success text-success',
                    'completed' => 'bg-soft-info text-info',
                    default => 'bg-soft-secondary text-secondary'
                };

                return '<span class="badge ' . $statusClass . '">' . $row->status_label . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.academic-year.action', compact('row'))->render();
            })
            ->rawColumns(['semester_label', 'status_label', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new academic year.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $semesters = AcademicSemesterEnum::options();

        return view('admin.pages.academic-year.create', compact('semesters'));
    }

    /**
     * Store a newly created academic year in storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(AcademicYearStoreRequest $request)
    {
        $academicYear = $this->academicYearService->createAcademicYear($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Tahun akademik berhasil ditambahkan',
            'data' => $academicYear,
        ]);
    }

    /**
     * Display the specified academic year.
     *
     * @return \Illuminate\View\View
     */
    public function show(AcademicYear $academicYear)
    {
        $academicYear = $this->academicYearService->getAcademicYearWithRelations($academicYear->id);
        $classrooms = $academicYear->classrooms;
        $teacherAssignments = $academicYear->teacherAssignments;

        return view('admin.pages.academic-year.show', compact('academicYear', 'classrooms', 'teacherAssignments'));
    }

    /**
     * Show the form for editing the specified academic year.
     *
     * @return \Illuminate\View\View
     */
    public function edit(AcademicYear $academicYear)
    {
        $semesters = AcademicSemesterEnum::options();

        return view('admin.pages.academic-year.edit', compact('academicYear', 'semesters'));
    }

    /**
     * Update the specified academic year in storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(AcademicYearUpdateRequest $request, AcademicYear $academicYear)
    {
        $academicYear = $this->academicYearService->updateAcademicYear($academicYear->id, $request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Tahun akademik berhasil diperbarui',
            'data' => $academicYear,
        ]);
    }

    /**
     * Remove the specified academic year from storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(AcademicYear $academicYear)
    {
        // Check if the academic year has related data before deleting
        if ($this->academicYearService->hasRelatedData($academicYear->id)) {
            return response()->json([
                'success' => false,
                'message' => 'Tahun akademik tidak dapat dihapus karena masih memiliki data terkait.',
            ], 422);
        }

        $this->academicYearService->deleteAcademicYear($academicYear);

        return response()->json([
            'success' => true,
            'message' => 'Tahun akademik berhasil dihapus',
        ]);
    }

    /**
     * Change the status of the specified academic year.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request, AcademicYear $academicYear)
    {
        $request->validate([
            'status' => 'required|string|in:planned,active,completed',
        ]);

        $this->academicYearService->changeAcademicYearStatus($academicYear->id, $request->status);

        return response()->json([
            'success' => true,
            'message' => 'Status tahun akademik berhasil diperbarui',
        ]);
    }

    /**
     * Get a list of all academic years for API.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAcademicYearsList()
    {
        try {
            $academicYears = $this->academicYearService->getAcademicYearsList();

            return response()->json([
                'success' => true,
                'message' => 'Daftar tahun akademik berhasil dimuat',
                'data' => $academicYears,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }
}
