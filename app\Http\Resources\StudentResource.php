<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'nis' => $this->nis,
            'nisn' => $this->nisn,
            'birth_place' => $this->birth_place,
            'birth_date' => $this->birth_date,
            'gender' => $this->gender,
            'religion' => $this->religion,
            'phone' => $this->phone,
            'address' => $this->address,
            'status' => $this->status,
            'entry_year' => $this->entry_year,
            'parent_info' => [
                'name' => $this->parent_name,
                'phone' => $this->parent_phone,
                'occupation' => $this->parent_occupation,
                'address' => $this->parent_address,
            ],
            'profile_picture' => $this->profile_picture ? asset('storage/'.$this->profile_picture) : null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
