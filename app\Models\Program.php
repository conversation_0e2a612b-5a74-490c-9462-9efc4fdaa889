<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Program extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'status',
    ];

    /**
     * Get the subjects for the program.
     */
    public function subjects(): Has<PERSON>any
    {
        return $this->hasMany(Subject::class);
    }

    /**
     * Get the classrooms for the program.
     */
    public function classrooms(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Classroom::class);
    }
}
