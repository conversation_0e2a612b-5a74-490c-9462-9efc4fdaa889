<?php

namespace App\Http\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use App\Models\Student;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Tentukan apakah pengguna berhak melakukan permintaan ini.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Dapatkan aturan validasi yang berlaku untuk permintaan tersebut.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('student');

        return [
            // Data user
            'name' => ['sometimes', 'string', 'max:255'],
            'username' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                'alpha_dash',
                Rule::unique('users', 'username')->ignore(Student::find($studentId)->user_id),
            ],
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore(Student::find($studentId)->user_id),
            ],
            'password' => ['sometimes', 'nullable', 'string', Password::defaults()],
            'phone_number' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
            'status' => ['sometimes', Rule::enum(UserStatus::class)],

            // Data profil siswa
            'nis' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nis')->ignore($studentId),
            ],
            'nisn' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nisn')->ignore($studentId),
            ],
            'birth_place' => ['sometimes', 'string', 'max:100'],
            'birth_date' => ['sometimes', 'date', 'before:today'],
            'gender' => ['sometimes', Rule::enum(GenderEnum::class)],
            'parent_name' => ['sometimes', 'string', 'max:255'],
            'parent_phone' => ['sometimes', 'nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
        ];
    }

    /**
     * Dapatkan nama atribut yang akan ditampilkan pada pesan kesalahan.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama lengkap',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'phone_number' => 'Nomor telepon',
            'status' => 'Status',
            'nis' => 'NIS',
            'nisn' => 'NISN',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
            'parent_name' => 'Nama orang tua/wali',
            'parent_phone' => 'Nomor telepon orang tua/wali',
        ];
    }

    /**
     * Dapatkan pesan kesalahan kustom untuk aturan validasi.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.unique' => 'Username sudah digunakan.',
            'email.unique' => 'Email sudah digunakan.',
            'email.email' => 'Format email tidak valid.',
            'password.min' => 'Password minimal harus terdiri dari :min karakter.',
            'nis.unique' => 'NIS sudah terdaftar.',
            'nisn.unique' => 'NISN sudah terdaftar.',
            'birth_date.before' => 'Tanggal lahir harus sebelum hari ini.',
            'gender.enum' => 'Jenis kelamin tidak valid.',
            'parent_phone.regex' => 'Format nomor telepon tidak valid. Gunakan angka, spasi, tanda tambah, kurung, atau strip.',
            'max' => ':attribute maksimal :max karakter.',
            'string' => ':attribute harus berupa teks.',
            'date' => ':attribute harus berupa tanggal yang valid.',
        ];
    }
}
