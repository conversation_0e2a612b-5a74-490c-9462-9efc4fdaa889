<?php

namespace Database\Seeders;

use App\Enums\ClassroomLevelEnum;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Program;
use App\Models\Teacher;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClassroomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $programs = Program::all();
        $academicYear = AcademicYear::where('status', 'active')->first();
        $teachers = Teacher::all();

        if (!$academicYear || $teachers->isEmpty()) {
            $this->command->error('Academic year or teachers not found. Please run their seeders first.');

            return;
        }

        // Clear existing classrooms to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Classroom::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        foreach ($programs as $program) {
            // Create classrooms for each program level (X, XI, XII)
            $levels = [
                ClassroomLevelEnum::X->value,
                ClassroomLevelEnum::XI->value,
                ClassroomLevelEnum::XII->value,
            ];

            foreach ($levels as $level) {
                // For each level, create multiple classrooms (A, B, C)
                for ($i = 1; $i <= 3; $i++) {
                    $className = strtoupper($level) . ' ' . $program->code . ' ' . chr(64 + $i); // A=65, B=66, C=67 in ASCII

                    // Assign a random teacher as the initial homeroom teacher
                    $teacher = $teachers->random();

                    Classroom::create([
                        'name' => $className,
                        'level' => $level,
                        'capacity' => rand(30, 40), // Random capacity between 30-40
                        'program_id' => $program->id,
                        'academic_year_id' => $academicYear->id,
                        'teacher_id' => $teacher->id,
                        'status' => 'active',
                        'description' => $program->name . ' - ' . ClassroomLevelEnum::getLabel($level) . ' Kelas ' . chr(64 + $i),
                    ]);
                }
            }
        }
    }
}
