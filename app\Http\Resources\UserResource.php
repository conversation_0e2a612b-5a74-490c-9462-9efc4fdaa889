<?php

namespace App\Http\Resources;

use App\Enums\RoleEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'username' => $this->username,
            'status' => $this->status,
            'roles' => $this->roles->pluck('name')->map(fn ($role) => [
                'name' => $role,
                'label' => RoleEnum::getLabel($role),
            ]),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),

            // Only include these when the relationships are loaded
            'teacher' => $this->when($this->relationLoaded('teacher'), function () {
                return $this->teacher ? [
                    'id' => $this->teacher->id,
                    'url' => route('admin.teachers.edit', $this->teacher->id),
                ] : null;
            }),

            'student' => $this->when($this->relationLoaded('student'), function () {
                return $this->student ? [
                    'id' => $this->student->id,
                    'url' => route('admin.students.edit', $this->student->id),
                ] : null;
            }),
        ];
    }
}
