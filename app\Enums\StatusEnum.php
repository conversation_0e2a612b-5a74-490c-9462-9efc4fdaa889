<?php

namespace App\Enums;

enum StatusEnum: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';

    /**
     * Get all status values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all statuses as key-value pairs (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::ACTIVE->value => 'Aktif',
            self::INACTIVE->value => 'Tidak Aktif',
        ];
    }

    /**
     * Get the display label for a status value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }

    /**
     * Get color class for the status
     */
    public function color(): string
    {
        return match ($this) {
            self::ACTIVE => 'success',
            self::INACTIVE => 'danger',
        };
    }

    /**
     * Convert to boolean
     */
    public function toBool(): bool
    {
        return $this === self::ACTIVE;
    }

    /**
     * Create from boolean
     */
    public static function fromBool(bool $value): self
    {
        return $value ? self::ACTIVE : self::INACTIVE;
    }
}
