<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AttendanceStatusEnum;
use App\Enums\AttendantTypeEnum;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\AttendanceRequests\AttendanceStoreRequest;
use App\Http\Requests\AttendanceRequests\AttendanceUpdateRequest;
use App\Services\AttendanceService;
use App\Services\ClassroomService;
use App\Services\ClassScheduleService;
use App\Services\StudentService;
use App\Services\TeacherService;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Symfony\Component\HttpFoundation\Response;

class AttendanceController extends Controller
{
    private AttendanceService $attendanceService;

    private ClassScheduleService $classScheduleService;

    private ClassroomService $classroomService;

    private StudentService $studentService;

    private TeacherService $teacherService;

    /**
     * AttendanceController constructor.
     */
    public function __construct(
        AttendanceService $attendanceService,
        ClassScheduleService $classScheduleService,
        ClassroomService $classroomService,
        StudentService $studentService,
        TeacherService $teacherService
    ) {
        $this->attendanceService = $attendanceService;
        $this->classScheduleService = $classScheduleService;
        $this->classroomService = $classroomService;
        $this->studentService = $studentService;
        $this->teacherService = $teacherService;
    }

    /**
     * Display a listing of attendances.
     *
     * @return View|JsonResponse
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $filters = $request->all();
            $filters['datatable'] = true;
            $attendances = $this->attendanceService->getAllAttendances($filters);

            return response()->json($attendances);
        }

        $classrooms = $this->classroomService->getAllActiveClassrooms();
        $teachers = $this->teacherService->getAllActiveTeachers();

        return view('admin.pages.attendance.index', [
            'classrooms' => $classrooms,
            'teachers' => $teachers,
            'attendanceStatuses' => AttendanceStatusEnum::options(),
            'attendantTypes' => AttendantTypeEnum::options(),
        ]);
    }

    /**
     * Show the form for creating a new attendance.
     */
    public function create(): View
    {
        $students = $this->studentService->getAllActiveStudents();
        $teachers = $this->teacherService->getAllActiveTeachers();
        $classrooms = $this->classroomService->getAllActiveClassrooms();

        return view('admin.pages.attendance.create', [
            'classSchedules' => $this->classScheduleService->getAllClassSchedules(),
            'students' => $students,
            'teachers' => $teachers,
            'classrooms' => $classrooms,
            'attendanceStatuses' => AttendanceStatusEnum::options(),
            'attendantTypes' => AttendantTypeEnum::options(),
        ]);
    }

    /**
     * Store a newly created attendance in storage.
     */
    public function store(AttendanceStoreRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();
            $this->attendanceService->recordAttendance($data);

            return redirect()->route('admin.attendances.index')
                ->with('success', 'Kehadiran berhasil dicatat.');
        } catch (BusinessLogicException|DatabaseException $e) {
            return back()->withInput()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified attendance.
     */
    public function show(int $id): View
    {
        try {
            $attendance = $this->attendanceService->getAttendanceById($id);

            return view('admin.pages.attendance.show', [
                'attendance' => $attendance,
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified attendance.
     */
    public function edit(int $id): View
    {
        try {
            $attendance = $this->attendanceService->getAttendanceById($id);

            return view('admin.pages.attendance.edit', [
                'attendance' => $attendance,
                'attendanceStatuses' => AttendanceStatusEnum::options(),
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified attendance in storage.
     */
    public function update(AttendanceUpdateRequest $request, int $id): RedirectResponse
    {
        try {
            $data = $request->validated();
            $this->attendanceService->updateAttendance($id, $data);

            return redirect()->route('admin.attendances.index')
                ->with('success', 'Kehadiran berhasil diperbarui.');
        } catch (BusinessLogicException|DatabaseException|NotFoundException $e) {
            return back()->withInput()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified attendance from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $this->attendanceService->deleteAttendance($id);

            return redirect()->route('admin.attendances.index')
                ->with('success', 'Kehadiran berhasil dihapus.');
        } catch (DatabaseException|NotFoundException $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get class attendance for a specific date.
     */
    public function getClassAttendance(Request $request): JsonResponse
    {
        try {
            $classScheduleId = $request->input('class_schedule_id');
            $date = $request->input('date', Carbon::now()->format('Y-m-d'));

            $attendances = $this->attendanceService->getClassAttendance($classScheduleId, $date);

            return response()->json([
                'success' => true,
                'data' => $attendances,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get attendance summary for a classroom in a specific month.
     *
     * @return JsonResponse|View
     */
    public function getAttendanceSummary(Request $request)
    {
        try {
            $classroomId = $request->input('classroom_id');
            $month = $request->input('month', Carbon::now()->format('Y-m'));
            $classrooms = $this->classroomService->getAllActiveClassrooms();

            // If no classroom is selected, just show the form
            if (! $classroomId) {
                return view('admin.pages.attendance.summary', [
                    'classrooms' => $classrooms,
                    'selectedMonth' => $month,
                ]);
            }

            // Validate classroom ID is an integer
            if (! is_numeric($classroomId)) {
                throw new \InvalidArgumentException('ID kelas tidak valid');
            }

            $summary = $this->attendanceService->getAttendanceSummary((int) $classroomId, $month);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'data' => $summary,
                ]);
            }

            return view('admin.pages.attendance.summary', [
                'summary' => $summary,
                'classrooms' => $classrooms,
                'selectedClassroomId' => $classroomId,
                'selectedMonth' => $month,
            ]);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], Response::HTTP_BAD_REQUEST);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Export attendance summary to CSV.
     *
     * @return HttpResponse|RedirectResponse
     */
    public function exportAttendance(Request $request)
    {
        try {
            $classroomId = $request->input('classroom_id');
            $month = $request->input('month', Carbon::now()->format('Y-m'));

            if (! $classroomId) {
                return back()->with('error', 'Kelas harus dipilih');
            }

            // Validate classroom ID is an integer
            if (! is_numeric($classroomId)) {
                return back()->with('error', 'ID kelas tidak valid');
            }

            $summary = $this->attendanceService->getAttendanceSummary((int) $classroomId, $month);
            $date = Carbon::createFromFormat('Y-m', $month);
            $monthName = $date->translatedFormat('F Y');

            // Create CSV content
            $filename = 'Laporan_Kehadiran_'.str_replace(' ', '_', $summary['classroom']).'_'.$month.'.csv';
            $headers = [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            ];

            // Create a callback to generate CSV content
            $callback = function () use ($summary) {
                $file = fopen('php://output', 'w');

                // Add UTF-8 BOM for Excel compatibility
                fwrite($file, "\xEF\xBB\xBF");

                // Add header
                fputcsv($file, ['LAPORAN KEHADIRAN SISWA']);
                fputcsv($file, [$summary['classroom'].' - '.$summary['month']]);
                fputcsv($file, []); // Empty line

                // Add summary info
                fputcsv($file, ['Jumlah Siswa:', $summary['total_students']]);
                fputcsv($file, ['Jumlah Hari:', $summary['total_days']]);
                fputcsv($file, ['Hadir:', $summary['attendance_by_status']['present']]);
                fputcsv($file, ['Tidak Hadir:', $summary['attendance_by_status']['absent']]);
                fputcsv($file, ['Terlambat:', $summary['attendance_by_status']['late']]);
                fputcsv($file, []); // Empty line

                // Add table header
                fputcsv($file, ['No', 'Nama Siswa', 'Hadir', 'Tidak Hadir', 'Terlambat', 'Persentase Kehadiran']);

                // Add data
                foreach ($summary['students'] as $index => $student) {
                    fputcsv($file, [
                        $index + 1,
                        $student['name'],
                        $student['present_count'],
                        $student['absent_count'],
                        $student['late_count'],
                        $student['attendance_percentage'].'%',
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);

        } catch (\Exception $e) {
            return back()->with('error', 'Gagal mengekspor data: '.$e->getMessage());
        }
    }

    /**
     * Get students by classroom for AJAX requests.
     */
    public function getStudentsByClassroom(Request $request): JsonResponse
    {
        try {
            $classroomId = $request->input('classroom_id');
            $classroom = $this->classroomService->getClassroomById($classroomId);
            $students = $classroom->currentStudents;

            return response()->json([
                'success' => true,
                'data' => $students->load('user'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get class schedules by classroom for AJAX requests.
     */
    public function getClassSchedulesByClassroom(Request $request): JsonResponse
    {
        try {
            $classroomId = $request->input('classroom_id');

            // Get classroom
            $classroom = $this->classroomService->getClassroomById($classroomId);

            // Get teacher assignments for this classroom
            $teacherAssignments = $classroom->teacherAssignments;

            // Get class schedules for these teacher assignments
            $classScheduleIds = [];
            foreach ($teacherAssignments as $assignment) {
                $schedules = $assignment->classSchedules;
                foreach ($schedules as $schedule) {
                    if ($schedule->status === 'active') {
                        $classScheduleIds[] = $schedule->id;
                    }
                }
            }

            // Get class schedules
            $classSchedules = $this->classScheduleService->getAllClassSchedules()
                ->whereIn('id', $classScheduleIds)
                ->load(['subject', 'lessonHour']);

            return response()->json([
                'success' => true,
                'data' => $classSchedules,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get teacher attendance summary (simple version)
     *
     * @return JsonResponse|View
     */
    public function getTeacherAttendanceSummary(Request $request)
    {
        try {
            $teacherId = $request->input('teacher_id');
            $month = $request->input('month', Carbon::now()->format('Y-m'));
            $teachers = $this->teacherService->getAllActiveTeachers();

            // If no teacher is selected, just show the form
            if (! $teacherId) {
                return view('admin.pages.attendance.teacher-summary', [
                    'teachers' => $teachers,
                    'selectedMonth' => $month,
                ]);
            }

            // Validate teacher ID is an integer
            if (! is_numeric($teacherId)) {
                throw new \InvalidArgumentException('ID guru tidak valid');
            }

            // Parse the month
            $date = Carbon::createFromFormat('Y-m', $month);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d');

            // Get the teacher
            $teacher = $this->teacherService->getTeacherById((int) $teacherId);

            // Get all attendance records for the teacher in the specified month
            $attendances = $this->attendanceService->getTeacherAttendance((int) $teacherId, [
                'date_from' => $startDate,
                'date_to' => $endDate,
            ]);

            // Prepare summary data
            $summary = [
                'teacher' => $teacher->user->name,
                'month' => $date->format('F Y'),
                'total_days' => $date->daysInMonth,
                'attendance_by_status' => [
                    AttendanceStatusEnum::PRESENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count(),
                    AttendanceStatusEnum::ABSENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::ABSENT)->count(),
                    AttendanceStatusEnum::LATE->value => $attendances->where('attendance_status', AttendanceStatusEnum::LATE)->count(),
                ],
                'attendance_records' => $attendances->map(function ($attendance) {
                    return [
                        'id' => $attendance->id,
                        'date' => $attendance->attendance_date->format('d-m-Y'),
                        'day' => $attendance->attendance_date->format('l'),
                        'time' => $attendance->attendance_date->format('H:i'),
                        'class_name' => $attendance->classSchedule->classroom->name,
                        'subject_name' => $attendance->classSchedule->subject->name,
                        'status' => $attendance->attendance_status->value,
                        'notes' => $attendance->notes,
                    ];
                })->toArray(),
                'attendance_percentage' => $attendances->count() > 0
                    ? round(($attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count() / $attendances->count()) * 100, 2)
                    : 0,
            ];

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'data' => $summary,
                ]);
            }

            return view('admin.pages.attendance.teacher-summary', [
                'summary' => $summary,
                'teachers' => $teachers,
                'selectedTeacherId' => $teacherId,
                'selectedMonth' => $month,
            ]);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], Response::HTTP_BAD_REQUEST);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get detailed teacher attendance summary with class schedule matching
     *
     * @return JsonResponse|View
     */
    public function getTeacherDetailedAttendanceSummary(Request $request)
    {
        try {
            $teacherId = $request->input('teacher_id');
            $month = $request->input('month', Carbon::now()->format('Y-m'));
            $teachers = $this->teacherService->getAllActiveTeachers();

            // If no teacher is selected, just show the form
            if (! $teacherId) {
                return view('admin.pages.attendance.teacher-detailed-summary', [
                    'teachers' => $teachers,
                    'selectedMonth' => $month,
                ]);
            }

            // Validate teacher ID is an integer
            if (! is_numeric($teacherId)) {
                throw new \InvalidArgumentException('ID guru tidak valid');
            }

            // Parse the month
            $date = Carbon::createFromFormat('Y-m', $month);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d');

            // Get the teacher
            $teacher = $this->teacherService->getTeacherById((int) $teacherId);

            // Get all attendance records for the teacher in the specified month
            $attendances = $this->attendanceService->getTeacherAttendance((int) $teacherId, [
                'date_from' => $startDate,
                'date_to' => $endDate,
            ]);

            // Group attendances by class schedule
            $attendancesBySchedule = $attendances->groupBy('class_schedule_id');

            // Get all class schedules for this teacher
            $classScheduleSummaries = [];
            $totalSchedules = 0;

            // Process each class schedule
            foreach ($attendancesBySchedule as $scheduleId => $scheduleAttendances) {
                if (empty($scheduleId)) {
                    continue;
                }

                $firstAttendance = $scheduleAttendances->first();
                if (! $firstAttendance || ! $firstAttendance->classSchedule) {
                    continue;
                }

                $classSchedule = $firstAttendance->classSchedule;
                $totalSchedules++;

                // Get the teacher assignment for this schedule
                $teacherAssignment = $classSchedule->teacherAssignment;

                // Calculate statistics for this class schedule
                $classScheduleSummaries[] = [
                    'id' => $classSchedule->id,
                    'classroom' => $classSchedule->teacherAssignment->classroom->name,
                    'subject' => $classSchedule->teacherAssignment->subject->name,
                    'day_of_week' => ucfirst($classSchedule->day_of_week),
                    'time' => $classSchedule->lessonHour->start_time.' - '.$classSchedule->lessonHour->end_time,
                    'is_homeroom_teacher' => $teacherAssignment->is_homeroom_teacher,
                    'total_attendances' => $scheduleAttendances->count(),
                    'present_count' => $scheduleAttendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count(),
                    'absent_count' => $scheduleAttendances->where('attendance_status', AttendanceStatusEnum::ABSENT)->count(),
                    'late_count' => $scheduleAttendances->where('attendance_status', AttendanceStatusEnum::LATE)->count(),
                    'attendance_percentage' => $scheduleAttendances->count() > 0
                        ? round(($scheduleAttendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count() / $scheduleAttendances->count()) * 100, 2)
                        : 0,
                    'attendances' => $scheduleAttendances->map(function ($attendance) {
                        return [
                            'id' => $attendance->id,
                            'date' => $attendance->attendance_date->format('d-m-Y'),
                            'day' => $attendance->attendance_date->format('l'),
                            'time' => $attendance->attendance_date->format('H:i'),
                            'status' => $attendance->attendance_status->value,
                            'notes' => $attendance->notes,
                        ];
                    })->sortBy('date')->values()->toArray(),
                ];
            }

            // Group class schedules by classroom and subject
            $classroomSubjectGroups = collect($classScheduleSummaries)->groupBy(function ($item) {
                return $item['classroom'].' - '.$item['subject'];
            })->map(function ($group) {
                $firstItem = $group->first();

                return [
                    'classroom' => $firstItem['classroom'],
                    'subject' => $firstItem['subject'],
                    'is_homeroom_teacher' => $firstItem['is_homeroom_teacher'],
                    'schedules' => $group->values()->toArray(),
                    'total_schedules' => $group->count(),
                    'total_attendances' => $group->sum('total_attendances'),
                    'present_count' => $group->sum('present_count'),
                    'absent_count' => $group->sum('absent_count'),
                    'late_count' => $group->sum('late_count'),
                    'attendance_percentage' => $group->sum('total_attendances') > 0
                        ? round(($group->sum('present_count') / $group->sum('total_attendances')) * 100, 2)
                        : 0,
                ];
            })->values()->toArray();

            // Prepare overall summary data
            $summary = [
                'teacher' => $teacher->user->name,
                'month' => $date->format('F Y'),
                'total_days' => $date->daysInMonth,
                'total_schedules' => $totalSchedules,
                'attendance_by_status' => [
                    AttendanceStatusEnum::PRESENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count(),
                    AttendanceStatusEnum::ABSENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::ABSENT)->count(),
                    AttendanceStatusEnum::LATE->value => $attendances->where('attendance_status', AttendanceStatusEnum::LATE)->count(),
                ],
                'classroom_subject_groups' => $classroomSubjectGroups,
                'overall_attendance_percentage' => $attendances->count() > 0
                    ? round(($attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count() / $attendances->count()) * 100, 2)
                    : 0,
            ];

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'data' => $summary,
                ]);
            }

            return view('admin.pages.attendance.teacher-detailed-summary', [
                'summary' => $summary,
                'teachers' => $teachers,
                'selectedTeacherId' => $teacherId,
                'selectedMonth' => $month,
            ]);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], Response::HTTP_BAD_REQUEST);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get global teacher attendance report based on class schedules
     *
     * @return JsonResponse|View
     */
    public function getGlobalTeacherAttendanceReport(Request $request)
    {
        try {
            $month = $request->input('month', Carbon::now()->format('Y-m'));
            $academicYearId = $request->input('academic_year_id');
            $programId = $request->input('program_id');

            // Get active academic years for the filter
            $academicYears = app(\App\Services\AcademicYearService::class)->getAllActiveAcademicYears();

            // Get programs for the filter
            $programs = app(\App\Services\ProgramService::class)->getAllActivePrograms();

            // If no academic year is selected, use the current active one
            if (! $academicYearId && $academicYears->isNotEmpty()) {
                $academicYearId = $academicYears->where('status', 'active')->first()->id ?? $academicYears->first()->id;
            }

            // Parse the month
            $date = Carbon::createFromFormat('Y-m', $month);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d');

            // Get all active teachers
            $teachers = $this->teacherService->getAllActiveTeachers();

            // Get all class schedules for the selected academic year and program
            $classScheduleQuery = app(\App\Models\ClassSchedule::class)
                ->with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'teacherAssignment.classroom', 'lessonHour'])
                ->whereHas('teacherAssignment', function ($query) use ($academicYearId, $programId) {
                    $query->where('academic_year_id', $academicYearId);

                    if ($programId) {
                        $query->whereHas('classroom', function ($q) use ($programId) {
                            $q->where('program_id', $programId);
                        });
                    }
                })
                ->where('status', 'active');

            $classSchedules = $classScheduleQuery->get();

            // Group schedules by teacher
            $teacherSchedules = [];
            foreach ($classSchedules as $schedule) {
                $teacherId = $schedule->teacherAssignment->teacher_id;

                if (! isset($teacherSchedules[$teacherId])) {
                    $teacher = $schedule->teacherAssignment->teacher;
                    $teacherSchedules[$teacherId] = [
                        'teacher_id' => $teacherId,
                        'teacher_name' => $teacher->user->name,
                        'schedules' => [],
                        'total_schedules' => 0,
                        'total_attendances' => 0,
                        'present_count' => 0,
                        'absent_count' => 0,
                        'late_count' => 0,
                        'attendance_percentage' => 0,
                    ];
                }

                $teacherSchedules[$teacherId]['schedules'][] = [
                    'id' => $schedule->id,
                    'day_of_week' => ucfirst($schedule->day_of_week),
                    'time' => $schedule->lessonHour->start_time.' - '.$schedule->lessonHour->end_time,
                    'classroom' => $schedule->teacherAssignment->classroom->name,
                    'subject' => $schedule->teacherAssignment->subject->name,
                ];

                $teacherSchedules[$teacherId]['total_schedules']++;
            }

            // Get attendance data for each teacher
            foreach ($teacherSchedules as $teacherId => &$teacherData) {
                // Get all attendance records for the teacher in the specified month
                $attendances = $this->attendanceService->getTeacherAttendance((int) $teacherId, [
                    'date_from' => $startDate,
                    'date_to' => $endDate,
                ]);

                $teacherData['total_attendances'] = $attendances->count();
                $teacherData['present_count'] = $attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count();
                $teacherData['absent_count'] = $attendances->where('attendance_status', AttendanceStatusEnum::ABSENT)->count();
                $teacherData['late_count'] = $attendances->where('attendance_status', AttendanceStatusEnum::LATE)->count();
                $teacherData['attendance_percentage'] = $attendances->count() > 0
                    ? round(($attendances->where('attendance_status', AttendanceStatusEnum::PRESENT)->count() / $attendances->count()) * 100, 2)
                    : 0;
            }

            // Convert to array and sort by attendance percentage (descending)
            $teacherSchedules = collect($teacherSchedules)->sortByDesc('attendance_percentage')->values()->toArray();

            // Prepare summary data
            $summary = [
                'month' => $date->format('F Y'),
                'total_days' => $date->daysInMonth,
                'total_teachers' => count($teacherSchedules),
                'total_schedules' => collect($teacherSchedules)->sum('total_schedules'),
                'total_attendances' => collect($teacherSchedules)->sum('total_attendances'),
                'present_count' => collect($teacherSchedules)->sum('present_count'),
                'absent_count' => collect($teacherSchedules)->sum('absent_count'),
                'late_count' => collect($teacherSchedules)->sum('late_count'),
                'overall_attendance_percentage' => collect($teacherSchedules)->sum('total_attendances') > 0
                    ? round((collect($teacherSchedules)->sum('present_count') / collect($teacherSchedules)->sum('total_attendances')) * 100, 2)
                    : 0,
                'teacher_schedules' => $teacherSchedules,
            ];

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'data' => $summary,
                ]);
            }

            return view('admin.pages.attendance.global-teacher-report', [
                'summary' => $summary,
                'academicYears' => $academicYears,
                'programs' => $programs,
                'selectedAcademicYearId' => $academicYearId,
                'selectedProgramId' => $programId,
                'selectedMonth' => $month,
            ]);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], Response::HTTP_BAD_REQUEST);
            }

            return back()->with('error', $e->getMessage());
        }
    }
}
