<?php

namespace Database\Seeders;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TeacherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teachers = [];
        $subjects = Subject::all();

        for ($i = 1; $i <= 10; $i++) {
            $user = User::create([
                'username' => 'teacher'.$i,
                'name' => 'Teacher '.$subjects[$i - 1]->name,
                'email' => 'teacher'.$i.'@example.com',
                'password' => Hash::make('password'),
                'phone_number' => '0812345678'.$i,
                'status' => UserStatus::Active->value,
                'remember_token' => Str::random(10),
            ]);
            $user->assignRole(RoleEnum::SUBJECT_TEACHER->value);

            $teachers[] = Teacher::create([
                'user_id' => $user->id,
                'birth_place' => 'City '.$i,
                'birth_date' => now()->subYears(30 + $i),
                'gender' => $i % 2 ? 'male' : 'female',
                'phone_number' => $user->phone_number,
                'full_address' => 'Address '.$i,
            ]);
        }
    }
}
