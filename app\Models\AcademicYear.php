<?php

namespace App\Models;

use App\Enums\AcademicSemesterEnum;
use App\Enums\AcademicYearStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AcademicYear extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'semester',
        'start_date',
        'end_date',
        'status',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'status' => AcademicYearStatusEnum::class,
        'semester' => AcademicSemesterEnum::class,
    ];

    public function classrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function classroomStudents(): HasMany
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the semester name attribute.
     */
    public function getSemesterNameAttribute(): string
    {
        return $this->semester?->getFormattedLabel() ?? 'Tidak Diketahui';
    }

    /**
     * Get the formatted name attribute (Tahun - Semester).
     */
    public function getFormattedNameAttribute(): string
    {
        return "{$this->name} - {$this->semester_name}";
    }

    /**
     * Get the formatted start date attribute (d M Y).
     */
    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date ? $this->start_date->format('d M Y') : '-';
    }

    /**
     * Get the formatted end date attribute (d M Y).
     */
    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date ? $this->end_date->format('d M Y') : '-';
    }

    /**
     * Get the formatted period attribute (start_date - end_date).
     */
    public function getFormattedPeriodAttribute(): string
    {
        return "{$this->formatted_start_date} - {$this->formatted_end_date}";
    }

    /**
     * Get the formatted status attribute.
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status?->value) {
            'planned' => 'Direncanakan',
            'active' => 'Aktif',
            'completed' => 'Selesai',
            default => 'Tidak Diketahui'
        };
    }

    // Custom semester attribute method removed
}
