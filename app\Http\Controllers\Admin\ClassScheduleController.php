<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\ClassScheduleException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleStoreRequest;
use App\Http\Requests\ClassScheduleRequests\ClassScheduleUpdateRequest;
use App\Models\Classroom;
use App\Models\ClassSchedule;
use App\Services\AcademicYearService;
use App\Services\ClassroomService;
use App\Services\ClassScheduleService;
use App\Services\LessonHourService;
use App\Services\TeacherAssignmentService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class ClassScheduleController extends Controller
{
    /**
     * Class schedule service instance
     */
    protected ClassScheduleService $classScheduleService;

    /**
     * Teacher assignment service instance
     */
    protected TeacherAssignmentService $teacherAssignmentService;

    /**
     * Lesson hour service instance
     */
    protected LessonHourService $lessonHourService;

    /**
     * Classroom service instance
     */
    protected ClassroomService $classroomService;

    /**
     * Academic year service instance
     */
    protected AcademicYearService $academicYearService;

    /**
     * ClassScheduleController constructor
     */
    public function __construct(
        ClassScheduleService $classScheduleService,
        TeacherAssignmentService $teacherAssignmentService,
        LessonHourService $lessonHourService,
        ClassroomService $classroomService,
        AcademicYearService $academicYearService
    ) {
        $this->classScheduleService = $classScheduleService;
        $this->teacherAssignmentService = $teacherAssignmentService;
        $this->lessonHourService = $lessonHourService;
        $this->classroomService = $classroomService;
        $this->academicYearService = $academicYearService;
    }

    /**
     * Display a listing of class schedules by classroom, academic year
     */
    public function index(Request $request): View
    {
        return view('admin.pages.class-schedule.index-vue', [
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
        ]);
    }

    /**
     * Show the form for creating a new class schedule
     *
     * @return View|RedirectResponse
     */
    public function create(Request $request)
    {
        $classroomId = $request->input('classroom_id');
        $academicYearId = $request->input('academic_year_id');

        if (! $classroomId || ! $academicYearId) {
            return redirect()->route('admin.class-schedules.index')
                ->with('error', 'Pilih kelas dan tahun akademik terlebih dahulu');
        }

        $selectedClassroom = $this->classroomService->getClassroomById($classroomId);
        $selectedClassroom->load('shift');
        $selectedAcademicYear = $this->academicYearService->getAcademicYearById($academicYearId);

        return view('admin.pages.class-schedule.create', [
            'teacherAssignments' => $this->teacherAssignmentService->getAllTeacherAssignments([
                'classroom_id' => $classroomId,
                'academic_year_id' => $academicYearId,
            ]),
            'lessonHours' => $this->lessonHourService->getLessonHoursByClassroomId($classroomId),
            'days' => config('constants.days'),
            'selectedClassroom' => $selectedClassroom,
            'selectedAcademicYear' => $selectedAcademicYear,
        ]);
    }

    /**
     * Store a newly created class schedule
     */
    public function store(ClassScheduleStoreRequest $request): JsonResponse
    {
        try {
            $classSchedule = $this->classScheduleService->createClassSchedule($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil disimpan',
                'data' => $classSchedule,
            ]);
        } catch (ClassScheduleException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            \Log::error('Error creating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan jadwal kelas',
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified class schedule
     */
    public function edit(ClassSchedule $classSchedule): View
    {
        $teacherAssignment = $classSchedule->teacherAssignment;
        $classroom = $teacherAssignment->classroom;
        $classroom->load('shift');
        $academicYear = $teacherAssignment->academicYear;

        return view('admin.pages.class-schedule.edit', [
            'classSchedule' => $classSchedule,
            'teacherAssignments' => $this->teacherAssignmentService->getAllTeacherAssignments([
                'classroom_id' => $classroom->id,
                'academic_year_id' => $academicYear->id,
            ]),
            'lessonHours' => $this->lessonHourService->getLessonHoursByClassroomId($classroom->id),
            'days' => config('constants.days'),
            'selectedClassroom' => $classroom,
            'selectedAcademicYear' => $academicYear,
        ]);
    }

    /**
     * Update the specified class schedule
     */
    public function update(ClassScheduleUpdateRequest $request, ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $result = $this->classScheduleService->updateClassSchedule($classSchedule->id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => $result
                    ? 'Jadwal kelas berhasil diperbarui'
                    : 'Tidak ada perubahan data',
            ]);
        } catch (ClassScheduleException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], $e->getStatusCode());
        } catch (\Exception $e) {
            \Log::error('Error updating class schedule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui jadwal kelas',
            ], 500);
        }
    }

    /**
     * Remove the specified class schedule
     */
    public function destroy(ClassSchedule $classSchedule): JsonResponse
    {
        try {
            $this->classScheduleService->deleteClassSchedule($classSchedule->id);

            return response()->json([
                'success' => true,
                'message' => 'Jadwal kelas berhasil dihapus',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get schedule data for AJAX requests
     */
    public function getData(Request $request): JsonResponse
    {
        $classroomId = $request->input('classroom_id');
        $academicYearId = $request->input('academic_year_id');

        if (! $classroomId || ! $academicYearId) {
            return response()->json([
                'success' => false,
                'message' => 'Pilih kelas dan tahun akademik terlebih dahulu',
            ], 400);
        }

        try {
            return response()->json([
                'success' => true,
                'data' => $this->classScheduleService->getScheduleData($classroomId, $academicYearId),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified class schedule
     *
     * @return JsonResponse|RedirectResponse
     */
    public function show(ClassSchedule $classSchedule)
    {
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $classSchedule,
            ]);
        }

        return redirect()->route('admin.class-schedules.edit', $classSchedule);
    }

    /**
     * Get schedule data for edit form
     */
    public function getEditData(ClassSchedule $classSchedule): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $classSchedule,
        ]);
    }
}
