<?php

namespace App\Http\Requests\UserRequests;

use App\Enums\RoleEnum;
use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'username' => ['required', 'string', 'max:100', 'unique:users,username'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'string', 'min:8'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'role' => ['required', 'string', 'in:' . implode(',', $this->getAllowedRoles())],
            'status' => ['nullable', 'numeric', 'in:0,1'],
        ];
    }

    /**
     * Get the allowed roles for user creation.
     * Excludes teacher roles which can only be created through the teacher module.
     *
     * @return array<string>
     */
    protected function getAllowedRoles(): array
    {
        $allRoles = RoleEnum::values();

        return array_filter($allRoles, function ($role) {
            return !str_contains($role, 'teacher') && !str_contains($role, 'student');
        });
    }

    public function messages(): array
    {
        return [
            'username.required' => 'Username wajib diisi.',
            'username.unique' => 'Username sudah digunakan.',
            'name.required' => 'Nama lengkap wajib diisi.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'role.required' => 'Role wajib dipilih.',
            'role.in' => 'Role yang dipilih tidak valid.',
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password minimal 8 karakter.',
        ];
    }
}
