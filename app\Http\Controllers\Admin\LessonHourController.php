<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\LessonHourRequests\LessonHourFilterRequest;
use App\Http\Requests\LessonHourRequests\LessonHourStoreRequest;
use App\Http\Requests\LessonHourRequests\LessonHourUpdateRequest;
use App\Services\ClassroomService;
use App\Services\LessonHourService;
use App\Services\ShiftService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class LessonHourController extends Controller
{
    /**
     * LessonHour service instance
     */
    protected LessonHourService $lessonHourService;

    /**
     * Classroom service instance
     */
    protected ClassroomService $classroomService;

    /**
     * Shift service instance
     */
    protected ShiftService $shiftService;

    /**
     * LessonHourController constructor
     */
    public function __construct(LessonHourService $lessonHourService, ClassroomService $classroomService, ShiftService $shiftService)
    {
        $this->lessonHourService = $lessonHourService;
        $this->classroomService = $classroomService;
        $this->shiftService = $shiftService;
    }

    /**
     * Display a listing of lesson hours
     *
     * @return View|JsonResponse
     */
    public function index(LessonHourFilterRequest $request)
    {
        $lessonHours = $this->lessonHourService->getAllLessonHours($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($lessonHours);
        }

        return view('admin.pages.lesson-hour.index', [
            'lessonHours' => $lessonHours,
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'shifts' => $this->shiftService->getAllActiveShifts(),
        ]);
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        $result = [];
        $counter = 0;

        foreach ($data as $item) {
            $counter++;
            $result[] = [
                'DT_RowIndex' => $counter,
                'id' => $item->id,
                'name' => $item->name,
                'time' => $item->formatted_start_time.' - '.$item->formatted_end_time,
                'sequence' => $item->sequence,
                'classroom' => $item->classroom ? $item->classroom->name : 'Global',
                'shift' => $item->shift ? $item->shift->name : 'Global',
                'action' => view('admin.pages.lesson-hour.action', compact('item'))->render(),
            ];
        }

        return response()->json([
            'draw' => request()->input('draw'),
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $result,
        ]);
    }

    /**
     * Show the form for creating a new lesson hour
     */
    public function create(): View
    {
        return view('admin.pages.lesson-hour.create', [
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'shifts' => $this->shiftService->getAllActiveShifts(),
        ]);
    }

    /**
     * Store a newly created lesson hour
     */
    public function store(LessonHourStoreRequest $request): JsonResponse
    {
        try {
            $lessonHour = $this->lessonHourService->createLessonHour($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil dibuat',
                'data' => $lessonHour,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Show the form for editing the specified lesson hour
     */
    public function edit(int $id): View
    {
        $lessonHour = $this->lessonHourService->getLessonHourById($id);

        return view('admin.pages.lesson-hour.edit', [
            'lessonHour' => $lessonHour,
            'classrooms' => $this->classroomService->getAllActiveClassrooms(),
            'shifts' => $this->shiftService->getAllActiveShifts(),
        ]);
    }

    /**
     * Update the specified lesson hour
     */
    public function update(LessonHourUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $result = $this->lessonHourService->updateLessonHour($id, $request->validated());

            if (! $result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tidak ada perubahan data',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil diperbarui',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Remove the specified lesson hour
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->lessonHourService->deleteLessonHour($id);

            return response()->json([
                'success' => true,
                'message' => 'Jam pelajaran berhasil dihapus',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}
