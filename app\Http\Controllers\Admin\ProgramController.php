<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProgramRequests\ProgramStoreRequest;
use App\Http\Requests\ProgramRequests\ProgramUpdateRequest;
use App\Models\Program;
use App\Services\ProgramService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProgramController extends Controller
{
    protected $programService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(ProgramService $programService)
    {
        $this->programService = $programService;
    }

    /**
     * Display a listing of the programs.
     *
     * @return View|JsonResponse
     */
    public function index(Request $request)
    {
        $programs = $this->programService->getAllPrograms($request->all());

        if ($request->ajax()) {
            return $this->datatableResponse($programs);
        }

        return view('admin.pages.program.index');
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->addColumn('classrooms_count', function ($row) {
                return $row->classrooms_count;
            })
            ->addColumn('subjects_count', function ($row) {
                return $row->subjects_count;
            })
            ->addColumn('action', function ($row) {
                return view('admin.pages.program.action', compact('row'))->render();
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new program.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.pages.program.create');
    }

    /**
     * Store a newly created program in storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(ProgramStoreRequest $request)
    {
        $program = $this->programService->createProgram($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Program berhasil ditambahkan',
            'data' => $program,
        ]);
    }

    /**
     * Display the specified program.
     *
     * @return \Illuminate\View\View
     */
    public function show(Program $program)
    {
        $classrooms = $this->programService->getProgramClassrooms($program->id);

        return view('admin.pages.program.show', compact('program', 'classrooms'));
    }

    /**
     * Show the form for editing the specified program.
     *
     * @return \Illuminate\View\View
     */
    public function edit(Program $program)
    {
        return view('admin.pages.program.edit', compact('program'));
    }

    /**
     * Update the specified program in storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(ProgramUpdateRequest $request, Program $program)
    {
        $program = $this->programService->updateProgram($program->id, $request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Program berhasil diperbarui',
            'data' => $program,
        ]);
    }

    /**
     * Remove the specified program from storage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Program $program)
    {
        $this->programService->deleteProgram($program->id);

        return response()->json([
            'success' => true,
            'message' => 'Program berhasil dihapus',
        ]);
    }

    /**
     * Get a list of all programs for API.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProgramsList()
    {
        $programs = $this->programService->getProgramsList();

        return response()->json($programs);
    }
}
