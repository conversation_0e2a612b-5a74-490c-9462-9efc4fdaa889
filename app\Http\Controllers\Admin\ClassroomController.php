<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ClassroomLevelEnum;
use App\Enums\StatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClassroomRequests\ClassroomStoreRequest;
use App\Http\Requests\ClassroomRequests\ClassroomUpdateRequest;
use App\Models\Classroom;
use App\Models\Student;
use App\Services\AcademicYearService;
use App\Services\ClassroomService;
use App\Services\ClassScheduleService;
use App\Services\LessonHourService;
use App\Services\ProgramService;
use App\Services\ShiftService;
use App\Services\TeacherService;
use DB;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ClassroomController extends Controller
{
    protected $classroomService;

    protected $programService;

    protected $teacherService;

    protected $academicYearService;

    protected $classScheduleService;

    protected $lessonHourService;

    protected $shiftService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        ClassroomService $classroomService,
        ProgramService $programService,
        TeacherService $teacherService,
        AcademicYearService $academicYearService,
        ClassScheduleService $classScheduleService,
        LessonHourService $lessonHourService,
        ShiftService $shiftService
    ) {
        $this->classroomService = $classroomService;
        $this->programService = $programService;
        $this->teacherService = $teacherService;
        $this->academicYearService = $academicYearService;
        $this->classScheduleService = $classScheduleService;
        $this->lessonHourService = $lessonHourService;
        $this->shiftService = $shiftService;
    }

    /**
     * Display a listing of the classrooms.
     *
     * @return View|JsonResponse
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $classrooms = $this->classroomService->getAllClassrooms($request->all());

            return DataTables::of($classrooms)
                ->addIndexColumn()
                ->addColumn('teacher_name', function ($classroom) {
                    return $classroom->teacher ? $classroom->teacher->user->name : 'N/A';
                })
                ->addColumn('program_name', function ($classroom) {
                    return $classroom->program ? $classroom->program->name : 'N/A';
                })
                ->addColumn('shift_name', function ($classroom) {
                    return $classroom->shift ? $classroom->shift->name : 'Tidak ada shift';
                })
                ->addColumn('academic_year', function ($classroom) {
                    if (!$classroom->academicYear) {
                        return 'N/A';
                    }

                    return $classroom->academicYear->formatted_name;
                })
                ->addColumn('capacity_status', function ($classroom) {
                    $current = $classroom->currentStudents()->count();

                    return "<span class='badge bg-info-subtle text-info'>{$current}/{$classroom->capacity}</span>";
                })
                ->addColumn('level_name', function ($classroom) {
                    return $classroom->level_name;
                })
                ->addColumn('status_label', function ($classroom) {
                    $status = $classroom->status;
                    if ($status === null) {
                        return "<span class='badge bg-secondary'>" . StatusEnum::getLabel(null) . '</span>';
                    }

                    // Menggunakan method color() dari enum untuk mendapatkan warna yang sesuai
                    $statusClass = 'bg-' . $status->color();

                    return "<span class='badge {$statusClass}'>" . StatusEnum::getLabel($status) . '</span>';
                })
                ->addColumn('academic_link', function ($classroom) {
                    return '<a href="' . route('admin.classrooms.academic-details', $classroom) . '" class="btn btn-sm btn-info"><i class="ri-book-open-line"></i> Akademik</a>';
                })
                ->addColumn('action', function ($classroom) {
                    return view('admin.components.button-actions-v2', [
                        'id' => $classroom->id,
                        'edit' => route('admin.classrooms.edit', $classroom->id),
                        'show' => route('admin.classrooms.show', $classroom->id),
                        'destroy' => route('admin.classrooms.destroy', $classroom->id),
                    ])->render();
                })
                ->rawColumns(['capacity_status', 'status_label', 'academic_link', 'action'])
                ->make(true);
        }

        return view('admin.pages.classroom.index', [
            'levels' => ClassroomLevelEnum::options(),
            'statuses' => StatusEnum::options(),
            'programs' => $this->programService->getAllActivePrograms(),
        ]);
    }

    /**
     * Show the form for creating a new classroom.
     */
    public function create(): View
    {
        return view('admin.pages.classroom.create', [
            'programs' => $this->programService->getAllActivePrograms(),
            'teachers' => $this->teacherService->getAllActiveTeachers(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
            'shifts' => $this->shiftService->getAllActiveShifts(),
            'levels' => ClassroomLevelEnum::options(),
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created classroom in storage.
     */
    public function store(ClassroomStoreRequest $request): JsonResponse
    {
        try {
            $classroom = $this->classroomService->createClassroom($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dibuat',
                'data' => $classroom,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified classroom.
     *
     * @param  int  $id
     */
    public function show($id): View
    {
        $classroom = $this->classroomService->getClassroomById($id);
        $students = $this->classroomService->getStudentsInClassroom($id);
        $availableStudents = $this->classroomService->getAvailableStudents($classroom->academic_year_id);

        return view('admin.pages.classroom.show', [
            'classroom' => $classroom,
            'availableStudents' => $availableStudents,
        ]);
    }

    /**
     * Show the form for editing the specified classroom.
     *
     * @param  int  $id
     */
    public function edit($id): View
    {
        $classroom = $this->classroomService->getClassroomById($id);

        return view('admin.pages.classroom.edit', [
            'classroom' => $classroom,
            'programs' => $this->programService->getAllActivePrograms(),
            'teachers' => $this->teacherService->getAllActiveTeachers(),
            'academicYears' => $this->academicYearService->getAllActiveAcademicYears(),
            'shifts' => $this->shiftService->getAllActiveShifts(),
            'levels' => ClassroomLevelEnum::options(),
            'statuses' => StatusEnum::options(),
        ]);
    }

    /**
     * Update the specified classroom in storage.
     *
     * @param  int  $id
     */
    public function update(ClassroomUpdateRequest $request, $id): JsonResponse
    {
        try {
            $this->classroomService->updateClassroom($id, $request->validated());
            $classroom = $this->classroomService->getClassroomById($id);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil diperbarui',
                'data' => $classroom,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified classroom from storage.
     */
    public function destroy(Classroom $classroom): JsonResponse
    {
        try {
            // Delete the classroom using the service
            $this->classroomService->deleteClassroom($classroom->id);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dihapus',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add students to a classroom.
     */
    public function addStudents(Request $request, Classroom $classroom): JsonResponse
    {
        try {
            $request->validate([
                'student_ids' => 'required|array',
                'student_ids.*' => 'exists:students,id',
            ]);

            // Use the service method to enroll students
            $this->classroomService->enrollStudentsToClassroom($classroom->id, $request->student_ids);

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil ditambahkan ke kelas',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove a student from a classroom.
     */
    public function removeStudent(Classroom $classroom, Student $student): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Use the service method to remove the student
            $this->classroomService->removeStudentFromClassroom($classroom->id, $student->id);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil dikeluarkan dari kelas',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Change the status of a classroom.
     */
    public function changeStatus(Request $request, Classroom $classroom): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|string|in:active,inactive',
            ]);

            // Use the service method to update the classroom
            $this->classroomService->updateClassroom($classroom->id, ['status' => $request->status]);

            return response()->json([
                'success' => true,
                'message' => 'Status kelas berhasil diperbarui',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get classrooms by academic year or classroom ID.
     */
    public function getClassroomsByAcademicYear(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'academic_year_id' => 'nullable|exists:academic_years,id',
                'classroom_id' => 'nullable|exists:classrooms,id',
                'active_only' => 'nullable|boolean',
            ]);

            $academicYearId = $request->academic_year_id;
            $classroomId = $request->classroom_id;
            $activeOnly = $request->boolean('active_only', false);

            // If classroom_id is provided, get that specific classroom
            if (!empty($classroomId)) {
                $classroom = $this->classroomService->getClassroomById($classroomId);
                $classroom->load('program'); // Make sure program relation is loaded

                return response()->json([
                    'success' => true,
                    'message' => 'Data kelas berhasil dimuat',
                    'data' => [
                        [
                            'id' => $classroom->id,
                            'name' => $classroom->name,
                            'level' => $classroom->level,
                            'capacity' => $classroom->capacity,
                            'program_id' => $classroom->program_id,
                        ],
                    ],
                ]);
            }

            // Otherwise, get classrooms by academic year
            if (empty($academicYearId)) {
                $classrooms = $activeOnly
                    ? $this->classroomService->getAllActiveClassrooms()
                    : $this->classroomService->getAllClassrooms();
            } else {
                $classrooms = $activeOnly
                    ? $this->classroomService->getActiveClassroomsByAcademicYear($academicYearId)
                    : $this->classroomService->getClassroomsByAcademicYear($academicYearId);
            }

            $mappedClassrooms = $classrooms->map(function ($classroom) {
                return [
                    'id' => $classroom->id,
                    'name' => $classroom->name,
                    'level' => $classroom->level,
                    'capacity' => $classroom->capacity,
                    'program_id' => $classroom->program_id,
                ];
            })->values();

            return response()->json([
                'success' => true,
                'message' => 'Daftar kelas berhasil dimuat',
                'data' => $mappedClassrooms,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a list of classrooms for filtering
     */
    public function getClassroomsList(): JsonResponse
    {
        $classrooms = $this->classroomService->getAllActiveClassrooms()
            ->map(function ($classroom) {
                return [
                    'id' => $classroom->id,
                    'name' => $classroom->name,
                ];
            })->values();

        return response()->json([
            'success' => true,
            'message' => 'Daftar kelas berhasil dimuat',
            'data' => $classrooms,
        ]);
    }

    /**
     * Show academic details for a classroom including schedules and teacher assignments.
     *
     * @param  Classroom  $classroom  The classroom model
     * @return View The academic details view
     */
    public function showAcademicDetails(Classroom $classroom)
    {
        $classroom = $this->classroomService->getClassroomById($classroom->id);
        $classroom->load([
            'academicYear',
            'teacherAssignments.teacher.user',
            'teacherAssignments.subject',
            'shift',
        ]);
        $teacherAssignments = $classroom->teacherAssignments;
        $lessonHours = $this->lessonHourService->getLessonHoursByClassroomId($classroom->id);
        $schedules = [];
        $dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        if (!$lessonHours->isEmpty()) {
            foreach ($lessonHours as $hour) {
                $schedules[$hour->id] = [
                    'hour' => $hour,
                    'days' => array_combine($dayKeys, array_fill(0, count($dayKeys), null)),
                ];
            }
        }
        $existingSchedules = $this->classScheduleService->getSchedulesByClassroomAndAcademicYear($classroom->id, $classroom->academic_year_id);
        foreach ($existingSchedules as $schedule) {
            $schedules[$schedule->lesson_hour_id]['days'][$schedule->day_of_week] = $schedule;
        }
        $days = [
            'monday' => 'Senin',
            'tuesday' => 'Selasa',
            'wednesday' => 'Rabu',
            'thursday' => 'Kamis',
            'friday' => 'Jumat',
        ];

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'classroom' => $classroom,
                'teacherAssignments' => $teacherAssignments,
                'schedules' => $schedules,
                'lessonHours' => $lessonHours,
                'days' => $days
            ]);
        }

        return view('admin.pages.classroom.academic-details', compact(
            'classroom',
            'teacherAssignments',
            'schedules',
            'days',
            'lessonHours'
        ));
    }
}
