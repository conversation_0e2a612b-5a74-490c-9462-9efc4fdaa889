@extends('admin.layouts.app')

@section('title', 'Siswa')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-students">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <button type="button" class="btn btn-outline-success" id="import-btn">
                                <i class="ri-upload-line align-bottom"></i> Import
                            </button>
                            <a href="{{ route('admin.students.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Baru
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Status Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    @foreach ($statuses as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Gender Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-gender" class="form-label">Jenis Kelamin</label>
                                <select class="form-select" data-choices name="gender" id="filter-gender">
                                    <option value="">Semua</option>
                                    @foreach ($genders as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Classroom Dropdown -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="filter-classroom" class="form-label">Kelas</label>
                                <select class="form-select" data-choices id="filter-classroom" name="classroom">
                                    <option value="">Semua Kelas</option>
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{ $classroom->id }}">{{ $classroom->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-12 col-sm-6 col-md-3">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama, NIS, NISN..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="students-table" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Lengkap</th>
                                    <th>Email</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Kelas</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">Import Data Siswa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="importForm" method="POST" action="{{ route('admin.students.import') }}" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">File Excel</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx, .xls, .csv" required>
                            <small class="text-muted">Format: .xlsx, .xls, .csv</small>
                        </div>
                        <div class="mb-3">
                            <a href="{{ route('admin.students.template') }}" class="text-primary">
                                <i class="ri-download-line me-1"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-success">Import</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin menghapus data siswa ini? Data yang dihapus tidak dapat dikembalikan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">Hapus</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var dataTable = $('#students-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.students.index') }}",
                    data: function(d) {
                        d.status = $('#filter-status').val();
                        d.gender = $('#filter-gender').val();
                        d.classroom_id = $('#filter-classroom').val();
                        d.search = $('#search-input').val();
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'nis',
                        name: 'nis'
                    },
                    {
                        data: 'user.name',
                        name: 'user.name'
                    },
                    {
                        data: 'user.email',
                        name: 'user.email'
                    },
                    {
                        data: 'gender',
                        name: 'gender'
                    },
                    {
                        data: 'classrooms',
                        name: 'classrooms',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [2, 'asc']
                ], // Order by name
                language: {
                    processing: '<div class="spinner-border text-primary m-1" role="status"></div>',
                    searchPlaceholder: "Cari...",
                    lengthMenu: "Tampilkan _MENU_ data",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                },
                drawCallback: function() {
                    // Update total count
                    $('#total-students').text(this.api().page.info().recordsTotal);

                    // Initialize tooltips
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Apply filters when changed
            $('#filter-status, #filter-gender, #filter-classroom').on('change', function() {
                dataTable.draw();
            });

            // Apply search filter
            $('#search-button').on('click', function() {
                dataTable.draw();
            });

            // Show import modal
            $('#import-btn').on('click', function() {
                $('#importModal').modal('show');
            });

            // Handle import form submission
            $('#importForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function() {
                        // Show loading state
                        $('#importForm button[type="submit"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengimpor...');
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success notification
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // Refresh datatable
                            dataTable.ajax.reload();

                            // Close modal
                            $('#importModal').modal('hide');
                        }
                    },
                    error: function(xhr) {
                        // Show error notification
                        Swal.fire({
                            title: 'Gagal!',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan saat mengimpor data',
                            icon: 'error'
                        });
                    },
                    complete: function() {
                        // Reset loading state
                        $('#importForm button[type="submit"]').prop('disabled', false).text('Import');
                    }
                });
            });

            // Handle export button click
            $('#export-btn').on('click', function() {
                var params = {
                    status: $('#filter-status').val(),
                    gender: $('#filter-gender').val(),
                    classroom_id: $('#filter-classroom').val(),
                    search: $('#search-input').val()
                };

                var url = "{{ route('admin.students.export') }}?" + $.param(params);
                window.location.href = url;
            });

            // Open delete confirmation modal
            $(document).on('click', '.delete-btn', function() {
                var id = $(this).data('id');
                $('#confirm-delete-btn').data('id', id);
                $('#deleteModal').modal('show');
            });

            // Handle delete confirmation
            $('#confirm-delete-btn').on('click', function() {
                var id = $(this).data('id');

                $.ajax({
                    url: "{{ url('admin/students') }}/" + id,
                    type: 'DELETE',
                    data: {
                        "_token": "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#deleteModal').modal('hide');

                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message,
                                showConfirmButton: false,
                                timer: 1500
                            });

                            // Refresh table
                            dataTable.ajax.reload();
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan pada server'
                        });
                    }
                });
            });

            // Clear validation errors when modal is closed
            $('.modal').on('hidden.bs.modal', function() {
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').remove();
                $(this).find('form')[0].reset();
            });
        });
    </script>
@endpush
