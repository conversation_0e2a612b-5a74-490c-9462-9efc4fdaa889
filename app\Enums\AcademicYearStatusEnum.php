<?php

namespace App\Enums;

enum AcademicYearStatusEnum: string
{
    case PLANNED = 'planned';
    case ACTIVE = 'active';
    case COMPLETED = 'completed';

    /**
     * Get all status options as an array
     */
    public static function options(): array
    {
        return [
            self::PLANNED->value => 'Direncanakan',
            self::ACTIVE->value => 'Aktif',
            self::COMPLETED->value => 'Selesai',
        ];
    }

    /**
     * Get the display label for a status value
     */
    public static function getLabel(?string $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        return self::options()[$value] ?? $value;
    }
}
