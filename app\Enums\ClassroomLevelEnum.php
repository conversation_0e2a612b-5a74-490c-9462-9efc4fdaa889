<?php

namespace App\Enums;

enum ClassroomLevelEnum: string
{
    case X = 'x';
    case XI = 'xi';
    case XII = 'xii';

    /**
     * Get all level values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all levels as key-value pairs (for dropdowns)
     */
    public static function options(): array
    {
        return [
            self::X->value => 'Kelas X',
            self::XI->value => 'Kelas XI',
            self::XII->value => 'Kelas XII',
        ];
    }

    /**
     * Get the display label for a level value
     */
    public static function getLabel(string|self|null $value): string
    {
        if ($value === null) {
            return 'Tidak Diketahui';
        }

        if ($value instanceof self) {
            $value = $value->value;
        }

        return self::options()[$value] ?? $value;
    }
}
