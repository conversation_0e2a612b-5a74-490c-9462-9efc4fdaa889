<?php

namespace Database\Seeders;

use App\Models\Shift;
use Illuminate\Database\Seeder;

class ShiftSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shifts = [
            [
                'name' => 'Pagi',
                'description' => 'Shift pagi (07:00 - 12:00)',
                'status' => 'active',
            ],
            [
                'name' => 'Siang',
                'description' => 'Shift siang (12:30 - 17:00)',
                'status' => 'active',
            ],
        ];

        foreach ($shifts as $shift) {
            Shift::create($shift);
        }
    }
}
